project(Thirdparty LANGUAGES CXX)

# openssl 模块（vcpkg）
find_package(OpenSSL CONFIG REQUIRED)
add_library(OpenSSL INTERFACE)
target_link_libraries(OpenSSL
        INTERFACE
        OpenSSL::SSL
        OpenSSL::Crypto
)

# Apache Arrow 模块（vcpkg）
find_package(Arrow CONFIG REQUIRED)
add_library(Arrow INTERFACE)
target_link_libraries(Arrow
        INTERFACE
        Arrow::arrow_shared
        Arrow::parquet_shared
)

# NanoArrow 模块（轻量级Arrow实现）
find_package(nanoarrow CONFIG)
if(nanoarrow_FOUND)
    add_library(NanoArrow INTERFACE)
    target_link_libraries(NanoArrow INTERFACE nanoarrow::nanoarrow)
    message(STATUS "使用独立的nanoarrow库")
else()
    # 使用DuckDB内置的nanoarrow支持作为后备方案
    add_library(NanoArrow INTERFACE)
    message(STATUS "使用DuckDB内置的nanoarrow支持")
endif()

add_subdirectory(Crashpad)
add_subdirectory(QtKeychain)
add_subdirectory(Sqlcipher)
add_subdirectory(DuckDB)