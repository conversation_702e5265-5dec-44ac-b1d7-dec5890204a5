/**
 * @file ArrowMFTWriter.cpp
 * @brief Apache Arrow集成的MFT数据写入器实现
 */

#include "ArrowMFTWriter.h"
#include <QDebug>
#include <QFileInfo>
#include <chrono>

ArrowMFTWriter::ArrowMFTWriter()
    : m_compressionType(arrow::Compression::SNAPPY)
    , m_chunkSize(50000)
    , m_memoryPool(arrow::default_memory_pool())
{
    m_schema = createMFTSchema();
    resetPerformanceStats();
}

ArrowMFTWriter::~ArrowMFTWriter() {
    // Arrow对象会自动清理
}

std::shared_ptr<arrow::Schema> ArrowMFTWriter::createMFTSchema() {
    return arrow::schema({
        arrow::field("id", arrow::int64()),
        arrow::field("file_name", arrow::utf8()),
        arrow::field("file_path", arrow::utf8()),
        arrow::field("file_size", arrow::int64()),
        arrow::field("mft_entry", arrow::int64()),
        arrow::field("is_directory", arrow::boolean()),
        arrow::field("is_deleted", arrow::boolean()),
        arrow::field("created_time", arrow::utf8()),
        arrow::field("modified_time", arrow::utf8()),
        arrow::field("accessed_time", arrow::utf8()),
        arrow::field("disk_identifier", arrow::utf8()),
        arrow::field("scan_time", arrow::utf8())
    });
}

std::shared_ptr<arrow::Table> ArrowMFTWriter::convertMFTToArrow(
    const std::vector<FileSystemInfoDuck>& fileInfos,
    int64_t startId) {
    
    auto start = std::chrono::high_resolution_clock::now();
    
    std::vector<std::shared_ptr<arrow::Array>> arrays;
    if (!buildArrowArrays(fileInfos, startId, arrays)) {
        return nullptr;
    }
    
    auto table = arrow::Table::Make(m_schema, arrays);
    
    auto end = std::chrono::high_resolution_clock::now();
    m_stats.conversionTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
    m_stats.recordsProcessed = fileInfos.size();
    
    return table;
}

bool ArrowMFTWriter::buildArrowArrays(const std::vector<FileSystemInfoDuck>& fileInfos,
                                     int64_t startId,
                                     std::vector<std::shared_ptr<arrow::Array>>& arrays) {
    try {
        const size_t numRecords = fileInfos.size();
        
        // 创建列构建器
        arrow::Int64Builder id_builder(m_memoryPool);
        arrow::StringBuilder file_name_builder(m_memoryPool);
        arrow::StringBuilder file_path_builder(m_memoryPool);
        arrow::Int64Builder file_size_builder(m_memoryPool);
        arrow::Int64Builder mft_entry_builder(m_memoryPool);
        arrow::BooleanBuilder is_directory_builder(m_memoryPool);
        arrow::BooleanBuilder is_deleted_builder(m_memoryPool);
        arrow::StringBuilder created_time_builder(m_memoryPool);
        arrow::StringBuilder modified_time_builder(m_memoryPool);
        arrow::StringBuilder accessed_time_builder(m_memoryPool);
        arrow::StringBuilder disk_identifier_builder(m_memoryPool);
        arrow::StringBuilder scan_time_builder(m_memoryPool);
        
        // 预分配容量
        auto status = id_builder.Reserve(numRecords);
        if (!status.ok()) {
            setError("Failed to reserve memory for id_builder: " + status.ToString());
            return false;
        }
        
        status = file_name_builder.Reserve(numRecords);
        if (!status.ok()) {
            setError("Failed to reserve memory for file_name_builder: " + status.ToString());
            return false;
        }
        
        // 批量添加数据
        for (size_t i = 0; i < numRecords; ++i) {
            const auto& info = fileInfos[i];
            
            // 添加各列数据
            auto append_status = id_builder.Append(startId + static_cast<int64_t>(i));
            if (!append_status.ok()) {
                setError("Failed to append id: " + append_status.ToString());
                return false;
            }
            
            append_status = file_name_builder.Append(info.getFileName());
            if (!append_status.ok()) {
                setError("Failed to append file_name: " + append_status.ToString());
                return false;
            }
            
            append_status = file_path_builder.Append(info.getFilePath());
            if (!append_status.ok()) {
                setError("Failed to append file_path: " + append_status.ToString());
                return false;
            }
            
            append_status = file_size_builder.Append(static_cast<int64_t>(info.getFileSize()));
            if (!append_status.ok()) {
                setError("Failed to append file_size: " + append_status.ToString());
                return false;
            }
            
            append_status = mft_entry_builder.Append(static_cast<int64_t>(info.getMftEntry()));
            if (!append_status.ok()) {
                setError("Failed to append mft_entry: " + append_status.ToString());
                return false;
            }
            
            append_status = is_directory_builder.Append(info.getIsDirectory());
            if (!append_status.ok()) {
                setError("Failed to append is_directory: " + append_status.ToString());
                return false;
            }
            
            append_status = is_deleted_builder.Append(info.getIsDeleted());
            if (!append_status.ok()) {
                setError("Failed to append is_deleted: " + append_status.ToString());
                return false;
            }
            
            append_status = created_time_builder.Append(info.getCreatedTime());
            if (!append_status.ok()) {
                setError("Failed to append created_time: " + append_status.ToString());
                return false;
            }
            
            append_status = modified_time_builder.Append(info.getModifiedTime());
            if (!append_status.ok()) {
                setError("Failed to append modified_time: " + append_status.ToString());
                return false;
            }
            
            append_status = accessed_time_builder.Append(info.getAccessedTime());
            if (!append_status.ok()) {
                setError("Failed to append accessed_time: " + append_status.ToString());
                return false;
            }
            
            append_status = disk_identifier_builder.Append(info.getDiskIdentifier());
            if (!append_status.ok()) {
                setError("Failed to append disk_identifier: " + append_status.ToString());
                return false;
            }
            
            append_status = scan_time_builder.Append(info.getScanTime());
            if (!append_status.ok()) {
                setError("Failed to append scan_time: " + append_status.ToString());
                return false;
            }
        }
        
        // 完成数组构建
        std::shared_ptr<arrow::Array> id_array;
        status = id_builder.Finish(&id_array);
        if (!status.ok()) {
            setError("Failed to finish id_array: " + status.ToString());
            return false;
        }
        
        std::shared_ptr<arrow::Array> file_name_array;
        status = file_name_builder.Finish(&file_name_array);
        if (!status.ok()) {
            setError("Failed to finish file_name_array: " + status.ToString());
            return false;
        }
        
        std::shared_ptr<arrow::Array> file_path_array;
        status = file_path_builder.Finish(&file_path_array);
        if (!status.ok()) {
            setError("Failed to finish file_path_array: " + status.ToString());
            return false;
        }
        
        std::shared_ptr<arrow::Array> file_size_array;
        status = file_size_builder.Finish(&file_size_array);
        if (!status.ok()) {
            setError("Failed to finish file_size_array: " + status.ToString());
            return false;
        }
        
        std::shared_ptr<arrow::Array> mft_entry_array;
        status = mft_entry_builder.Finish(&mft_entry_array);
        if (!status.ok()) {
            setError("Failed to finish mft_entry_array: " + status.ToString());
            return false;
        }
        
        std::shared_ptr<arrow::Array> is_directory_array;
        status = is_directory_builder.Finish(&is_directory_array);
        if (!status.ok()) {
            setError("Failed to finish is_directory_array: " + status.ToString());
            return false;
        }
        
        std::shared_ptr<arrow::Array> is_deleted_array;
        status = is_deleted_builder.Finish(&is_deleted_array);
        if (!status.ok()) {
            setError("Failed to finish is_deleted_array: " + status.ToString());
            return false;
        }
        
        std::shared_ptr<arrow::Array> created_time_array;
        status = created_time_builder.Finish(&created_time_array);
        if (!status.ok()) {
            setError("Failed to finish created_time_array: " + status.ToString());
            return false;
        }
        
        std::shared_ptr<arrow::Array> modified_time_array;
        status = modified_time_builder.Finish(&modified_time_array);
        if (!status.ok()) {
            setError("Failed to finish modified_time_array: " + status.ToString());
            return false;
        }
        
        std::shared_ptr<arrow::Array> accessed_time_array;
        status = accessed_time_builder.Finish(&accessed_time_array);
        if (!status.ok()) {
            setError("Failed to finish accessed_time_array: " + status.ToString());
            return false;
        }
        
        std::shared_ptr<arrow::Array> disk_identifier_array;
        status = disk_identifier_builder.Finish(&disk_identifier_array);
        if (!status.ok()) {
            setError("Failed to finish disk_identifier_array: " + status.ToString());
            return false;
        }
        
        std::shared_ptr<arrow::Array> scan_time_array;
        status = scan_time_builder.Finish(&scan_time_array);
        if (!status.ok()) {
            setError("Failed to finish scan_time_array: " + status.ToString());
            return false;
        }
        
        // 添加到数组列表
        arrays = {
            id_array,
            file_name_array,
            file_path_array,
            file_size_array,
            mft_entry_array,
            is_directory_array,
            is_deleted_array,
            created_time_array,
            modified_time_array,
            accessed_time_array,
            disk_identifier_array,
            scan_time_array
        };
        
        return true;
        
    } catch (const std::exception& e) {
        setError("Exception in buildArrowArrays: " + std::string(e.what()));
        return false;
    }
}

bool ArrowMFTWriter::writeToParquet(std::shared_ptr<arrow::Table> table,
                                   const std::string& filename) {
    if (!table) {
        setError("Table is null");
        return false;
    }

    auto start = std::chrono::high_resolution_clock::now();

    try {
        // 创建输出文件流
        auto result = arrow::io::FileOutputStream::Open(filename);
        if (!result.ok()) {
            setError("Failed to open output file: " + result.status().ToString());
            return false;
        }
        std::shared_ptr<arrow::io::FileOutputStream> outfile = result.ValueOrDie();

        // 设置Parquet写入属性
        parquet::WriterProperties::Builder props_builder;
        props_builder.compression(static_cast<parquet::Compression::type>(m_compressionType));
        props_builder.write_batch_size(m_chunkSize);
        auto props = props_builder.build();

        // 设置Arrow写入属性
        auto arrow_props = parquet::ArrowWriterProperties::Builder().build();

        // 写入Parquet文件
        auto write_result = parquet::arrow::WriteTable(*table, m_memoryPool, outfile,
                                                      m_chunkSize, props, arrow_props);
        if (!write_result.ok()) {
            setError("Failed to write Parquet file: " + write_result.ToString());
            return false;
        }

        // 关闭文件
        auto close_result = outfile->Close();
        if (!close_result.ok()) {
            setError("Failed to close output file: " + close_result.ToString());
            return false;
        }

        auto end = std::chrono::high_resolution_clock::now();
        m_stats.writeTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();

        // 获取文件大小
        QFileInfo fileInfo(QString::fromStdString(filename));
        if (fileInfo.exists()) {
            m_stats.fileSizeBytes = fileInfo.size();
            // 估算压缩比（假设原始数据大小）
            int64_t estimatedRawSize = table->num_rows() * 200; // 估算每行200字节
            if (estimatedRawSize > 0) {
                m_stats.compressionRatio = static_cast<double>(m_stats.fileSizeBytes) / estimatedRawSize;
            }
        }

        return true;

    } catch (const std::exception& e) {
        setError("Exception in writeToParquet: " + std::string(e.what()));
        return false;
    }
}

bool ArrowMFTWriter::writeMFTToParquet(const std::vector<FileSystemInfoDuck>& fileInfos,
                                      const std::string& filename,
                                      int64_t startId) {
    auto table = convertMFTToArrow(fileInfos, startId);
    if (!table) {
        return false;
    }

    return writeToParquet(table, filename);
}

int64_t ArrowMFTWriter::validateParquetFile(const std::string& filename) {
    try {
        // 打开Parquet文件
        auto result = arrow::io::ReadableFile::Open(filename);
        if (!result.ok()) {
            setError("Failed to open Parquet file for validation: " + result.status().ToString());
            return -1;
        }
        std::shared_ptr<arrow::io::ReadableFile> infile = result.ValueOrDie();

        // 创建Parquet读取器
        std::unique_ptr<parquet::arrow::FileReader> reader;
        auto reader_result = parquet::arrow::OpenFile(infile, m_memoryPool, &reader);
        if (!reader_result.ok()) {
            setError("Failed to create Parquet reader: " + reader_result.ToString());
            return -1;
        }

        // 读取表
        std::shared_ptr<arrow::Table> table;
        auto read_result = reader->ReadTable(&table);
        if (!read_result.ok()) {
            setError("Failed to read Parquet table: " + read_result.ToString());
            return -1;
        }

        return table->num_rows();

    } catch (const std::exception& e) {
        setError("Exception in validateParquetFile: " + std::string(e.what()));
        return -1;
    }
}

std::shared_ptr<arrow::Schema> ArrowMFTWriter::getMFTSchema() {
    return m_schema;
}

void ArrowMFTWriter::setWriteOptions(arrow::Compression::type compressionType, int64_t chunkSize) {
    m_compressionType = compressionType;
    m_chunkSize = chunkSize;
}

std::string ArrowMFTWriter::getLastError() const {
    return m_lastError;
}

ArrowMFTWriter::PerformanceStats ArrowMFTWriter::getPerformanceStats() const {
    return m_stats;
}

void ArrowMFTWriter::resetPerformanceStats() {
    m_stats = PerformanceStats();
}

void ArrowMFTWriter::setError(const std::string& error) {
    m_lastError = error;
    qWarning() << "ArrowMFTWriter Error:" << QString::fromStdString(error);
}
