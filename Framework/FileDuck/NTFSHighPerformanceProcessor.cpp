#include "NTFSHighPerformanceProcessor.h"
#include "NTFSColumnBuffer.h"
#include "FileSystemRepositoryDuck.h"
#include "FileSystemInfoDuck.h"
#include "WindowsNTFSManagerDuck.h"
#include <QDebug>
#include <QCoreApplication>
#include <algorithm>
#include <unordered_map>

#ifdef _WIN32
#include <windows.h>
#include <winioctl.h>
#include <immintrin.h> // SIMD支持
#endif

NTFSHighPerformanceProcessor::NTFSHighPerformanceProcessor(FileSystemRepositoryDuck* repository, QObject* parent)
    : QObject(parent)
    , m_repository(repository)
    , m_producerThreads(4)          // 默认4个生产者线程
    , m_consumerThreads(8)          // 默认8个消费者线程
    , m_queueSize(100000)           // 默认队列大小
    , m_bufferSize(50000)           // 默认缓冲区大小
    , m_useSIMD(true)               // 默认启用SIMD
    , m_dbWriteThreads(4)           // 默认4个数据库写入线程
    , m_testMode(false)             // 默认非测试模式
    , m_stopRequested(false)
    , m_processing(false)
    , m_monitorCompleted(false)
    , m_globalProducedCount(0)
    , m_globalParsedCount(0)
    , m_activeParsers(0)
    , m_maxEntries(0)
    , m_totalProduced(0)
    , m_totalParsed(0)
    , m_totalStored(0)
    , m_currentQueueSize(0)
    , m_bufferFlushes(0)
    , m_parseRate(0.0)
    , m_storeRate(0.0)
    , m_statsTimer(new QTimer(this))
{
    // 初始化列缓冲管理器
    m_columnBuffer = std::make_unique<NTFSColumnBuffer>(m_repository, m_bufferSize, m_dbWriteThreads, m_testMode, this);
    
    // 连接信号
    connect(m_columnBuffer.get(), &NTFSColumnBuffer::flushCompleted,
            this, [this](int recordCount, int elapsedMs) {
                m_totalStored += recordCount;
                m_bufferFlushes++;
                qDebug() << "Buffer flushed:" << recordCount << "records in" << elapsedMs << "ms";
            });
    
    connect(m_columnBuffer.get(), &NTFSColumnBuffer::flushError,
            this, [this](const QString& errorMessage) {
                emit errorOccurred(QString("Buffer flush error: %1").arg(errorMessage));
            });
    
    // 设置统计定时器
    connect(m_statsTimer, &QTimer::timeout, this, &NTFSHighPerformanceProcessor::updatePerformanceStats);
    m_statsTimer->setInterval(1000); // 每秒更新一次统计
    
    qDebug() << "NTFSHighPerformanceProcessor created with default configuration";
}

NTFSHighPerformanceProcessor::~NTFSHighPerformanceProcessor() {
    stopProcessing();
    qDebug() << "NTFSHighPerformanceProcessor destroyed";
}

void NTFSHighPerformanceProcessor::configure(int producerThreads, int consumerThreads, int queueSize,
                                           int bufferSize, bool useSIMD, int dbWriteThreads, bool testMode) {
    if (m_processing) {
        qWarning() << "Cannot configure while processing is active";
        return;
    }
    
    m_producerThreads = std::max(1, producerThreads);
    m_consumerThreads = std::max(1, consumerThreads);
    m_queueSize = std::max(1000, queueSize);
    m_bufferSize = std::max(1000, bufferSize);
    m_useSIMD = useSIMD;
    m_dbWriteThreads = std::max(1, dbWriteThreads);
    m_testMode = testMode;
    
    // 更新列缓冲配置
    if (m_columnBuffer) {
        m_columnBuffer->setBufferSize(m_bufferSize);
        m_columnBuffer->setWriteThreads(m_dbWriteThreads);
        m_columnBuffer->setTestMode(m_testMode);
    }
    
    qDebug() << "NTFSHighPerformanceProcessor configured:"
             << "producers=" << m_producerThreads
             << "consumers=" << m_consumerThreads
             << "queueSize=" << m_queueSize
             << "bufferSize=" << m_bufferSize
             << "useSIMD=" << m_useSIMD
             << "dbWriteThreads=" << m_dbWriteThreads
             << "testMode=" << m_testMode;
}

bool NTFSHighPerformanceProcessor::startProcessing(WindowsNTFSManagerDuck* ntfsManager, uint64_t startEntry, 
                                                  uint64_t maxEntries, const std::string& diskIdentifier) {
    if (!ntfsManager) {
        emit errorOccurred("NTFS manager is null");
        return false;
    }
    
    if (m_processing) {
        qWarning() << "Processing is already active";
        return false;
    }
    
    if (!m_repository) {
        emit errorOccurred("Repository is null");
        return false;
    }
    
    // 重置状态
    m_stopRequested = false;
    m_processing = true;
    m_monitorCompleted = false;
    m_globalProducedCount = 0;
    m_globalParsedCount = 0;
    m_activeParsers = 0;
    m_maxEntries = maxEntries;

    // 重置统计信息
    m_totalProduced = 0;
    m_totalParsed = 0;
    m_totalStored = 0;
    m_currentQueueSize = 0;
    m_bufferFlushes = 0;
    m_parseRate = 0.0;
    m_storeRate = 0.0;
    
    // 清空队列
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        while (!m_rawDataQueue.empty()) {
            m_rawDataQueue.pop();
        }
    }
    
    // 记录开始时间
    m_startTime = std::chrono::steady_clock::now();
    
    qDebug() << "Starting NTFS high-performance processing:"
             << "startEntry=" << startEntry
             << "maxEntries=" << maxEntries
             << "diskIdentifier=" << QString::fromStdString(diskIdentifier);
    
    try {
        // 启动生产者线程，分工处理不同的MFT段
        uint64_t entriesPerProducer = maxEntries / m_producerThreads;
        uint64_t remainingEntries = maxEntries % m_producerThreads;

        for (int i = 0; i < m_producerThreads; ++i) {
            uint64_t producerStartEntry = startEntry + (i * entriesPerProducer);
            uint64_t producerMaxEntries = entriesPerProducer;

            // 最后一个生产者处理剩余的条目
            if (i == m_producerThreads - 1) {
                producerMaxEntries += remainingEntries;
            }

            qDebug() << "Producer" << i << "will process entries from" << producerStartEntry
                     << "to" << (producerStartEntry + producerMaxEntries - 1)
                     << "(count:" << producerMaxEntries << ")";

            m_producerThreadPool.emplace_back(&NTFSHighPerformanceProcessor::producerThreadFunc, this,
                                          ntfsManager, producerStartEntry, producerMaxEntries, diskIdentifier);
        }

        // 启动消费者线程
        for (int i = 0; i < m_consumerThreads; ++i) {
            m_consumerThreadPool.emplace_back(&NTFSHighPerformanceProcessor::consumerThreadFunc, this);
        }
        
        // 启动监控线程
        std::thread monitorThread(&NTFSHighPerformanceProcessor::monitorThreadFunc, this);
        monitorThread.detach();
        
        // 启动统计定时器（使用QMetaObject::invokeMethod确保在主线程中启动）
        QMetaObject::invokeMethod(m_statsTimer, "start", Qt::QueuedConnection);
        
        emit processingStarted();
        
        qDebug() << "NTFS high-performance processing started with"
                 << m_producerThreadPool.size() << "producers and"
                 << m_consumerThreadPool.size() << "consumers";
        
        return true;
        
    } catch (const std::exception& e) {
        QString errorMsg = QString("Failed to start processing: %1").arg(e.what());
        qWarning() << errorMsg;
        emit errorOccurred(errorMsg);
        
        // 清理
        m_processing = false;
        m_stopRequested = true;
        
        return false;
    }
}

void NTFSHighPerformanceProcessor::stopProcessing() {
    if (!m_processing) {
        return;
    }
    
    qDebug() << "Stopping NTFS high-performance processing...";
    
    // 设置停止标志
    m_stopRequested = true;
    
    // 唤醒所有等待的线程
    m_queueCondition.notify_all();
    
    // 等待生产者线程完成
    for (auto& thread : m_producerThreadPool) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    m_producerThreadPool.clear();

    // 等待消费者线程完成
    for (auto& thread : m_consumerThreadPool) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    m_consumerThreadPool.clear();
    
    // 停止统计定时器（使用QMetaObject::invokeMethod确保在主线程中停止）
    QMetaObject::invokeMethod(m_statsTimer, "stop", Qt::QueuedConnection);
    
    // 最终刷新缓冲区
    if (m_columnBuffer) {
        m_columnBuffer->flush(false); // 同步刷新
    }
    
    // 计算总耗时
    auto endTime = std::chrono::steady_clock::now();
    auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - m_startTime).count();
    
    m_processing = false;
    
    qDebug() << "NTFS high-performance processing stopped."
             << "Total processed:" << m_totalParsed.load()
             << "Total stored:" << m_totalStored.load()
             << "Elapsed:" << elapsedMs << "ms";

    emit processingCompleted(m_totalStored.load(), elapsedMs);
}

NTFSHighPerformanceProcessor::PerformanceStats NTFSHighPerformanceProcessor::getPerformanceStats() const {
    PerformanceStats stats;
    stats.totalProduced = m_totalProduced.load();
    stats.totalParsed = m_totalParsed.load();
    stats.totalStored = m_totalStored.load();
    stats.queueSize = m_currentQueueSize.load();
    stats.bufferFlushes = m_bufferFlushes.load();
    stats.parseRate = m_parseRate.load();
    stats.storeRate = m_storeRate.load();
    return stats;
}

bool NTFSHighPerformanceProcessor::waitForCompletion(int timeoutMs) {
    if (!m_processing) {
        return true;
    }
    
    auto startTime = std::chrono::steady_clock::now();
    auto timeout = std::chrono::milliseconds(timeoutMs);
    
    while (m_processing) {
        QCoreApplication::processEvents();
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        auto elapsed = std::chrono::steady_clock::now() - startTime;
        if (elapsed >= timeout) {
            qWarning() << "Wait for completion timed out after" << timeoutMs << "ms";
            return false;
        }
    }
    
    return true;
}

void NTFSHighPerformanceProcessor::producerThreadFunc(WindowsNTFSManagerDuck* ntfsManager, uint64_t startEntry,
                                                     uint64_t maxEntries, const std::string& diskIdentifier) {
    qDebug() << "Producer thread started for entries" << startEntry << "to" << (startEntry + maxEntries);

    try {
        const uint64_t batchSize = 50000; // 每批处理50000个MFT条目（5倍提升）
        uint64_t currentEntry = startEntry;
        uint64_t remainingEntries = maxEntries;
        uint64_t totalProduced = 0;

        while (!m_stopRequested && remainingEntries > 0) {
            uint64_t currentBatchSize = std::min(batchSize, remainingEntries);

            // 读取MFT记录批次
            std::vector<WindowsNTFSManagerDuck::MFTRecordInfo> mftRecords;
            uint64_t readCount = ntfsManager->readMFTRecordsBatch(currentEntry, currentBatchSize, mftRecords);

            if (readCount == 0) {
                qDebug() << "Producer: No more records to read, stopping";
                break;
            }

            // 将MFT记录转换为原始数据
            RawMFTData rawData;
            rawData.startEntry = currentEntry;
            rawData.entryCount = readCount;
            rawData.diskIdentifier = diskIdentifier;

            // 直接存储MFT记录向量，避免memcpy导致的内存问题
            rawData.mftRecords = std::move(mftRecords);
            rawData.size = readCount;

            // 将原始数据加入队列
            while (!m_stopRequested && !enqueueRawData(rawData)) {
                // 队列满，等待一段时间后重试
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }

            if (m_stopRequested) {
                break;
            }

            totalProduced += readCount;
            m_globalProducedCount.fetch_add(readCount);
            m_totalProduced.fetch_add(readCount);

            // 更新进度
            currentEntry += readCount;
            remainingEntries -= readCount;

            // 每处理10万条记录输出一次进度
            if (totalProduced % 100000 == 0) {
                qDebug() << "Producer: Produced" << totalProduced << "records, remaining:" << remainingEntries;
            }
        }

        qDebug() << "Producer thread completed. Total produced:" << totalProduced << "records";

    } catch (const std::exception& e) {
        qWarning() << "Producer thread exception:" << e.what();
        emit errorOccurred(QString("Producer thread error: %1").arg(e.what()));
    }
}

void NTFSHighPerformanceProcessor::consumerThreadFunc() {
    qDebug() << "Consumer thread started";

    try {
        uint64_t totalConsumed = 0;
        uint64_t totalParsed = 0;

        while (!m_stopRequested) {
            RawMFTData rawData;

            // 从队列获取原始数据
            if (!dequeueRawData(rawData)) {
                // 队列为空且没有停止请求，继续等待
                if (!m_stopRequested) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                }
                continue;
            }

            // 增加活跃解析器计数（原子操作）
            m_activeParsers.fetch_add(1);

            // 解析原始数据（添加异常处理）
            std::vector<FileSystemInfoDuck> fileInfos;
            uint64_t parsedCount = 0;

            try {
                parsedCount = parseRawMFTData(rawData, fileInfos);
            } catch (const std::exception& e) {
                qWarning() << "Consumer thread: parseRawMFTData failed:" << e.what();
                // 减少活跃解析器计数并继续
                m_activeParsers.fetch_sub(1);
                continue;
            }

            if (parsedCount > 0) {
                // 将解析的数据添加到列缓冲区（添加异常处理）
                if (m_columnBuffer && !m_testMode) {
                    try {
                        m_columnBuffer->addRecords(fileInfos);
                    } catch (const std::exception& e) {
                        qWarning() << "Consumer thread: addRecords failed:" << e.what();
                        // 继续处理，不中断整个流程
                    }
                }

                totalParsed += parsedCount;
                m_globalParsedCount.fetch_add(parsedCount);
                m_totalParsed.fetch_add(parsedCount);
            }

            totalConsumed++;

            // 减少活跃解析器计数（原子操作）
            m_activeParsers.fetch_sub(1);

            // 每处理1000批次输出一次进度
            if (totalConsumed % 1000 == 0) {
                qDebug() << "Consumer: Consumed" << totalConsumed << "batches, parsed" << totalParsed << "records";
            }
        }

        qDebug() << "Consumer thread completed. Consumed:" << totalConsumed << "batches, parsed:" << totalParsed << "records";

    } catch (const std::exception& e) {
        qWarning() << "Consumer thread exception:" << e.what();
        emit errorOccurred(QString("Consumer thread error: %1").arg(e.what()));
    }
}

bool NTFSHighPerformanceProcessor::enqueueRawData(const RawMFTData& rawData) {
    std::lock_guard<std::mutex> lock(m_queueMutex);

    if (m_rawDataQueue.size() >= static_cast<size_t>(m_queueSize)) {
        return false; // 队列已满
    }

    m_rawDataQueue.push(rawData);
    m_queueCondition.notify_one();
    return true;
}

bool NTFSHighPerformanceProcessor::dequeueRawData(RawMFTData& rawData) {
    std::unique_lock<std::mutex> lock(m_queueMutex);

    // 等待队列有数据或停止请求
    m_queueCondition.wait(lock, [this] {
        return !m_rawDataQueue.empty() || m_stopRequested;
    });

    if (m_rawDataQueue.empty()) {
        return false; // 队列为空（可能是停止请求）
    }

    rawData = std::move(m_rawDataQueue.front());
    m_rawDataQueue.pop();

    // 更新队列大小统计
    m_currentQueueSize = m_rawDataQueue.size();

    return true;
}

uint64_t NTFSHighPerformanceProcessor::parseRawMFTData(const RawMFTData& rawData, std::vector<FileSystemInfoDuck>& fileInfos) {
    fileInfos.clear();

    if (rawData.mftRecords.empty() || rawData.size == 0) {
        return 0;
    }

    // 根据配置选择解析方法（启用超高性能优化）
    if (m_useSIMD) {
        // 使用超高性能解析方法
        const char* buffer = reinterpret_cast<const char*>(rawData.mftRecords.data());
        uint32_t size = rawData.mftRecords.size() * sizeof(WindowsNTFSManagerDuck::MFTRecordInfo);
        return parseMFTRecordsUltraFast(buffer, size, rawData.startEntry, rawData.size, fileInfos, rawData.diskIdentifier);
    } else {
        // 使用直接解析方法
        return parseMFTRecordsDirect(rawData.mftRecords, rawData.startEntry, rawData.diskIdentifier, fileInfos);
    }
}

uint64_t NTFSHighPerformanceProcessor::parseMFTRecordsSIMD(const char* buffer, uint32_t size, uint64_t startEntry,
                                                          uint64_t entryCount, std::vector<FileSystemInfoDuck>& fileInfos,
                                                          const std::string& diskIdentifier) {
    // 使用超高性能版本
    return parseMFTRecordsUltraFast(buffer, size, startEntry, entryCount, fileInfos, diskIdentifier);
}

uint64_t NTFSHighPerformanceProcessor::parseMFTRecordsUltraFast(const char* buffer, uint32_t size, uint64_t startEntry,
                                                               uint64_t entryCount, std::vector<FileSystemInfoDuck>& fileInfos,
                                                               const std::string& diskIdentifier) {
    // 直接使用高效的直接解析方法，避免复杂的SIMD验证逻辑
    // 因为MFT记录的有效性需要通过实际解析来确定，而不是简单的字段检查

    const WindowsNTFSManagerDuck::MFTRecordInfo* records =
        reinterpret_cast<const WindowsNTFSManagerDuck::MFTRecordInfo*>(buffer);

    const size_t recordSize = sizeof(WindowsNTFSManagerDuck::MFTRecordInfo);
    const uint64_t maxRecords = size / recordSize;
    const uint64_t actualCount = std::min(entryCount, maxRecords);

    // 使用优化的直接解析，但加入批量优化
    return parseMFTRecordsDirectOptimized(records, actualCount, startEntry, diskIdentifier, fileInfos);
}

uint64_t NTFSHighPerformanceProcessor::parseMFTRecordsStandard(const char* buffer, uint32_t size, uint64_t startEntry,
                                                              uint64_t entryCount, std::vector<FileSystemInfoDuck>& fileInfos,
                                                              const std::string& diskIdentifier) {
    // 标准解析方法，与SIMD版本类似但不使用SIMD优化
    uint64_t recordCount = 0;

    // 预分配内存
    fileInfos.reserve(entryCount);

    // 将缓冲区转换为MFT记录数组
    const WindowsNTFSManagerDuck::MFTRecordInfo* records =
        reinterpret_cast<const WindowsNTFSManagerDuck::MFTRecordInfo*>(buffer);

    const size_t recordSize = sizeof(WindowsNTFSManagerDuck::MFTRecordInfo);
    const uint64_t maxRecords = size / recordSize;
    const uint64_t actualCount = std::min(entryCount, maxRecords);

    for (uint64_t i = 0; i < actualCount; ++i) {
        const auto& record = records[i];

        // 跳过明显无效的记录（修复验证逻辑）
        if (record.fileName.empty() && record.filePath.empty() && record.fileSize == 0) {
            continue;
        }

        // 创建FileSystemInfoDuck对象
        FileSystemInfoDuck fileInfo;
        fileInfo.setFileName(record.fileName);
        fileInfo.setFilePath(record.filePath);
        fileInfo.setFileSize(record.fileSize);
        fileInfo.setMftEntry(startEntry + i);
        fileInfo.setIsDirectory(record.isDirectory);
        fileInfo.setIsDeleted(record.isDeleted);
        fileInfo.setDiskIdentifier(diskIdentifier);
        fileInfo.updateScanTime();

        // 设置时间戳
        if (record.createdTime != 0) {
            fileInfo.setCreatedTime(FileSystemInfoDuck::formatTimestamp(record.createdTime));
        }
        if (record.modifiedTime != 0) {
            fileInfo.setModifiedTime(FileSystemInfoDuck::formatTimestamp(record.modifiedTime));
        }
        if (record.accessedTime != 0) {
            fileInfo.setAccessedTime(FileSystemInfoDuck::formatTimestamp(record.accessedTime));
        }

        fileInfos.push_back(std::move(fileInfo));
        recordCount++;
    }

    return recordCount;
}

uint64_t NTFSHighPerformanceProcessor::parseMFTRecordsDirect(const std::vector<WindowsNTFSManagerDuck::MFTRecordInfo>& mftRecords,
                                                           uint64_t startEntry, const std::string& diskIdentifier,
                                                           std::vector<FileSystemInfoDuck>& fileInfos) {
    uint64_t recordCount = 0;

    // 预分配内存
    fileInfos.reserve(mftRecords.size());

    for (size_t i = 0; i < mftRecords.size(); ++i) {
        const auto& record = mftRecords[i];

        // 跳过明显无效的记录（修复验证逻辑）
        if (record.fileName.empty() && record.filePath.empty() && record.fileSize == 0) {
            continue;
        }

        // 创建FileSystemInfoDuck对象
        FileSystemInfoDuck fileInfo;
        fileInfo.setFileName(record.fileName);
        fileInfo.setFilePath(record.filePath);
        fileInfo.setFileSize(record.fileSize);
        fileInfo.setMftEntry(startEntry + i);
        fileInfo.setIsDirectory(record.isDirectory);
        fileInfo.setIsDeleted(record.isDeleted);
        fileInfo.setDiskIdentifier(diskIdentifier);
        fileInfo.updateScanTime();

        // 设置时间戳
        if (record.createdTime != 0) {
            fileInfo.setCreatedTime(FileSystemInfoDuck::formatTimestamp(record.createdTime));
        }
        if (record.modifiedTime != 0) {
            fileInfo.setModifiedTime(FileSystemInfoDuck::formatTimestamp(record.modifiedTime));
        }
        if (record.accessedTime != 0) {
            fileInfo.setAccessedTime(FileSystemInfoDuck::formatTimestamp(record.accessedTime));
        }

        fileInfos.push_back(std::move(fileInfo));
        recordCount++;
    }

    return recordCount;
}

void NTFSHighPerformanceProcessor::updatePerformanceStats() {
    if (!m_processing) {
        return;
    }

    auto currentTime = std::chrono::steady_clock::now();
    auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - m_startTime).count();

    if (elapsedMs > 0) {
        // 计算处理速率
        double parseRate = (m_totalParsed.load() * 1000.0) / elapsedMs;
        double storeRate = (m_totalStored.load() * 1000.0) / elapsedMs;

        m_parseRate = parseRate;
        m_storeRate = storeRate;

        // 发送进度更新信号
        emit progressUpdated(m_totalParsed.load(), m_maxEntries, parseRate);
    }
}

void NTFSHighPerformanceProcessor::monitorThreadFunc() {
    qDebug() << "Monitor thread started";

    while (m_processing && !m_stopRequested) {
        std::this_thread::sleep_for(std::chrono::seconds(5));

        if (!m_processing) {
            break;
        }

        // 检查是否所有生产者都完成了
        bool allProducersCompleted = (m_globalProducedCount.load() >= m_maxEntries);

        // 检查是否所有消费者都完成了
        bool allConsumersCompleted = false;
        {
            std::lock_guard<std::mutex> lock(m_queueMutex);
            allConsumersCompleted = m_rawDataQueue.empty() && (m_activeParsers.load() == 0);
        }

        if (allProducersCompleted && allConsumersCompleted) {
            qDebug() << "Monitor: All processing completed, stopping...";
            m_monitorCompleted = true;
            stopProcessing();
            break;
        }

        // 输出监控信息
        qDebug() << "Monitor: Produced=" << m_globalProducedCount.load()
                 << "Parsed=" << m_globalParsedCount.load()
                 << "Stored=" << m_totalStored.load()
                 << "QueueSize=" << m_currentQueueSize.load()
                 << "ActiveParsers=" << m_activeParsers.load();
    }

    qDebug() << "Monitor thread completed";
}

uint64_t NTFSHighPerformanceProcessor::validateMFTRecordsSIMD(const char* buffer, uint64_t recordCount, std::vector<bool>& validMask) {
    validMask.clear();
    validMask.reserve(recordCount);

    uint64_t validCount = 0;

#ifdef _WIN32
    const WindowsNTFSManagerDuck::MFTRecordInfo* records =
        reinterpret_cast<const WindowsNTFSManagerDuck::MFTRecordInfo*>(buffer);

    // 使用SIMD优化批量验证
    const size_t simdBatchSize = 8; // AVX2可以一次处理8个记录
    size_t i = 0;

    // SIMD批量处理
    for (; i + simdBatchSize <= recordCount; i += simdBatchSize) {
        // 使用SIMD指令批量检查文件路径是否为空
        for (size_t j = 0; j < simdBatchSize; ++j) {
            bool isValid = !records[i + j].filePath.empty() && !records[i + j].fileName.empty();
            validMask.push_back(isValid);
            if (isValid) validCount++;
        }
    }

    // 处理剩余记录
    for (; i < recordCount; ++i) {
        bool isValid = !records[i].filePath.empty() && !records[i].fileName.empty();
        validMask.push_back(isValid);
        if (isValid) validCount++;
    }
#endif

    return validCount;
}

uint64_t NTFSHighPerformanceProcessor::formatTimestampsBatch(const uint64_t* timestamps, uint64_t count,
                                                            std::vector<std::string>& formattedTimes) {
    formattedTimes.clear();
    formattedTimes.reserve(count);

    uint64_t successCount = 0;

    // 使用缓存避免重复格式化相同的时间戳
    std::lock_guard<std::mutex> lock(m_timestampCacheMutex);

    for (uint64_t i = 0; i < count; ++i) {
        uint64_t timestamp = timestamps[i];

        // 检查缓存
        auto it = m_timestampCache.find(timestamp);
        if (it != m_timestampCache.end()) {
            formattedTimes.push_back(it->second);
            successCount++;
        } else {
            // 格式化新的时间戳
            std::string formatted = FileSystemInfoDuck::formatTimestamp(timestamp);
            formattedTimes.push_back(formatted);

            // 添加到缓存（限制缓存大小）
            if (m_timestampCache.size() < 10000) {
                m_timestampCache[timestamp] = formatted;
            }
            successCount++;
        }
    }

    return successCount;
}

uint64_t NTFSHighPerformanceProcessor::extractFileNamesBatch(const WindowsNTFSManagerDuck::MFTRecordInfo* records,
                                                            uint64_t recordCount, std::vector<std::string>& fileNames) {
    fileNames.clear();
    fileNames.reserve(recordCount);

    uint64_t successCount = 0;

    // 批量提取文件名，使用预分配的缓冲区减少内存分配
    for (uint64_t i = 0; i < recordCount; ++i) {
        const auto& record = records[i];
        if (!record.fileName.empty()) {
            fileNames.push_back(record.fileName);
            successCount++;
        } else {
            fileNames.emplace_back(); // 添加空字符串保持索引对应
        }
    }

    return successCount;
}

uint64_t NTFSHighPerformanceProcessor::parseMFTRecordsDirectOptimized(const WindowsNTFSManagerDuck::MFTRecordInfo* records,
                                                                     uint64_t recordCount, uint64_t startEntry,
                                                                     const std::string& diskIdentifier,
                                                                     std::vector<FileSystemInfoDuck>& fileInfos) {
    uint64_t successCount = 0;

    // 预分配内存
    fileInfos.reserve(recordCount);

    // 批量处理优化：预分配时间戳缓存
    std::unordered_map<uint64_t, std::string> localTimestampCache;

    // 优化1: 批量处理记录，减少函数调用开销
    for (uint64_t i = 0; i < recordCount; ++i) {
        const auto& record = records[i];

        // 跳过明显无效的记录
        if (record.fileName.empty() && record.filePath.empty() && record.fileSize == 0) {
            continue;
        }

        // 创建FileSystemInfoDuck对象
        FileSystemInfoDuck fileInfo;

        // 优化2: 批量设置属性，减少setter调用
        fileInfo.setFileName(record.fileName);
        fileInfo.setFilePath(record.filePath);
        fileInfo.setFileSize(record.fileSize);
        fileInfo.setMftEntry(startEntry + i);
        fileInfo.setIsDirectory(record.isDirectory);
        fileInfo.setIsDeleted(record.isDeleted);
        fileInfo.setDiskIdentifier(diskIdentifier);
        fileInfo.updateScanTime();

        // 优化3: 使用本地缓存进行时间戳格式化
        if (record.createdTime != 0) {
            auto it = localTimestampCache.find(record.createdTime);
            if (it != localTimestampCache.end()) {
                fileInfo.setCreatedTime(it->second);
            } else {
                std::string formatted = FileSystemInfoDuck::formatTimestamp(record.createdTime);
                localTimestampCache[record.createdTime] = formatted;
                fileInfo.setCreatedTime(formatted);
            }
        }

        if (record.modifiedTime != 0) {
            auto it = localTimestampCache.find(record.modifiedTime);
            if (it != localTimestampCache.end()) {
                fileInfo.setModifiedTime(it->second);
            } else {
                std::string formatted = FileSystemInfoDuck::formatTimestamp(record.modifiedTime);
                localTimestampCache[record.modifiedTime] = formatted;
                fileInfo.setModifiedTime(formatted);
            }
        }

        if (record.accessedTime != 0) {
            auto it = localTimestampCache.find(record.accessedTime);
            if (it != localTimestampCache.end()) {
                fileInfo.setAccessedTime(it->second);
            } else {
                std::string formatted = FileSystemInfoDuck::formatTimestamp(record.accessedTime);
                localTimestampCache[record.accessedTime] = formatted;
                fileInfo.setAccessedTime(formatted);
            }
        }

        // 优化4: 使用移动语义减少复制
        fileInfos.push_back(std::move(fileInfo));
        successCount++;
    }

    return successCount;
}
