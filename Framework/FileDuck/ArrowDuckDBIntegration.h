/**
 * @file ArrowDuckDBIntegration.h
 * @brief Apache Arrow与DuckDB集成
 * 
 * 该类负责将Apache Arrow/Parquet数据导入到DuckDB数据库，
 * 实现高性能的批量数据导入，突破传统DuckDB Appender的性能瓶颈。
 */

#ifndef ARROWDUCKDBINTEGRATION_H
#define ARROWDUCKDBINTEGRATION_H

#include <memory>
#include <string>
#include <vector>

// DuckDB headers
#include <duckdb.h>

// Arrow headers
#include <arrow/api.h>
#include <arrow/io/api.h>

// Project headers
#include "FileSystemInfoDuck.h"

/**
 * @class ArrowDuckDBIntegration
 * @brief Arrow与DuckDB集成，实现高性能数据导入
 */
class ArrowDuckDBIntegration {
public:
    /**
     * @brief 构造函数
     */
    ArrowDuckDBIntegration();
    
    /**
     * @brief 析构函数
     */
    ~ArrowDuckDBIntegration();

    /**
     * @brief 从Parquet文件导入数据到DuckDB
     * @param connection DuckDB连接
     * @param parquetFile Parquet文件路径
     * @param tableName 目标表名
     * @return 导入的记录数，失败返回-1
     */
    int64_t importFromParquet(duckdb_connection connection,
                             const std::string& parquetFile,
                             const std::string& tableName = "file_system_info");

    /**
     * @brief 直接从Arrow表导入数据到DuckDB
     * @param connection DuckDB连接
     * @param table Arrow表
     * @param tableName 目标表名
     * @return 导入的记录数，失败返回-1
     */
    int64_t importFromArrowTable(duckdb_connection connection,
                                std::shared_ptr<arrow::Table> table,
                                const std::string& tableName = "file_system_info");

    /**
     * @brief 批量导入MFT数据到DuckDB
     * @param connection DuckDB连接
     * @param fileInfos MFT文件信息列表
     * @param startId 起始ID
     * @param tableName 目标表名
     * @return 导入的记录数，失败返回-1
     */
    int64_t importMFTData(duckdb_connection connection,
                         const std::vector<FileSystemInfoDuck>& fileInfos,
                         int64_t startId = 0,
                         const std::string& tableName = "file_system_info");

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息字符串
     */
    std::string getLastError() const;

    /**
     * @brief 获取性能统计信息
     */
    struct PerformanceStats {
        int64_t recordsImported = 0;
        int64_t importTimeMs = 0;
        double recordsPerSecond = 0.0;
    };

    /**
     * @brief 获取性能统计
     * @return 性能统计结构
     */
    PerformanceStats getPerformanceStats() const;

    /**
     * @brief 重置性能统计
     */
    void resetPerformanceStats();

private:
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);

    /**
     * @brief 执行SQL语句
     * @param connection DuckDB连接
     * @param sql SQL语句
     * @return 成功返回true，失败返回false
     */
    bool executeSql(duckdb_connection connection, const std::string& sql);

private:
    std::string m_lastError;           // 最后的错误信息
    PerformanceStats m_stats;          // 性能统计
};

#endif // ARROWDUCKDBINTEGRATION_H
