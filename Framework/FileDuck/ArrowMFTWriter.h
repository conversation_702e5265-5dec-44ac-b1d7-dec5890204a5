/**
 * @file ArrowMFTWriter.h
 * @brief Apache Arrow集成的MFT数据写入器
 * 
 * 该类负责将MFT数据转换为Apache Arrow格式，并通过Parquet文件
 * 实现高性能的批量数据库写入，突破传统DuckDB Appender的性能瓶颈。
 */

#ifndef ARROWMFTWRITER_H
#define ARROWMFTWRITER_H

#include <memory>
#include <vector>
#include <string>

// Apache Arrow headers
#include <arrow/api.h>
#include <arrow/io/api.h>
#include <arrow/compute/api.h>
#include <parquet/arrow/writer.h>
#include <parquet/arrow/reader.h>

// Project headers
#include "FileSystemInfoDuck.h"

/**
 * @class ArrowMFTWriter
 * @brief 高性能MFT数据写入器，使用Apache Arrow和Parquet格式
 * 
 * 该类实现了以下优化策略：
 * 1. 将MFT数据转换为Arrow列式格式
 * 2. 使用Parquet格式进行高效存储
 * 3. 支持批量数据库导入
 * 4. 内存优化和压缩
 */
class ArrowMFTWriter {
public:
    /**
     * @brief 构造函数
     */
    ArrowMFTWriter();
    
    /**
     * @brief 析构函数
     */
    ~ArrowMFTWriter();

    /**
     * @brief 将MFT数据转换为Arrow Table
     * @param fileInfos MFT文件信息列表
     * @param startId 起始ID
     * @return Arrow Table，如果失败返回nullptr
     */
    std::shared_ptr<arrow::Table> convertMFTToArrow(
        const std::vector<FileSystemInfoDuck>& fileInfos,
        int64_t startId = 0);

    /**
     * @brief 将Arrow Table写入Parquet文件
     * @param table Arrow表
     * @param filename 输出文件名
     * @return 成功返回true，失败返回false
     */
    bool writeToParquet(std::shared_ptr<arrow::Table> table, 
                       const std::string& filename);

    /**
     * @brief 批量写入MFT数据到Parquet文件
     * @param fileInfos MFT文件信息列表
     * @param filename 输出文件名
     * @param startId 起始ID
     * @return 成功返回true，失败返回false
     */
    bool writeMFTToParquet(const std::vector<FileSystemInfoDuck>& fileInfos,
                          const std::string& filename,
                          int64_t startId = 0);

    /**
     * @brief 从Parquet文件读取数据验证
     * @param filename Parquet文件名
     * @return 读取的记录数，失败返回-1
     */
    int64_t validateParquetFile(const std::string& filename);

    /**
     * @brief 获取MFT数据的Arrow Schema
     * @return Arrow Schema
     */
    std::shared_ptr<arrow::Schema> getMFTSchema();

    /**
     * @brief 设置Parquet写入选项
     * @param compressionType 压缩类型（默认SNAPPY）
     * @param chunkSize 块大小（默认50000）
     */
    void setWriteOptions(arrow::Compression::type compressionType = arrow::Compression::SNAPPY,
                        int64_t chunkSize = 50000);

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息字符串
     */
    std::string getLastError() const;

    /**
     * @brief 获取性能统计信息
     */
    struct PerformanceStats {
        int64_t recordsProcessed = 0;
        int64_t conversionTimeMs = 0;
        int64_t writeTimeMs = 0;
        int64_t fileSizeBytes = 0;
        double compressionRatio = 0.0;
    };

    /**
     * @brief 获取性能统计
     * @return 性能统计结构
     */
    PerformanceStats getPerformanceStats() const;

    /**
     * @brief 重置性能统计
     */
    void resetPerformanceStats();

private:
    /**
     * @brief 创建MFT数据的Arrow Schema
     * @return Arrow Schema
     */
    std::shared_ptr<arrow::Schema> createMFTSchema();

    /**
     * @brief 构建Arrow列数据
     * @param fileInfos MFT文件信息列表
     * @param startId 起始ID
     * @param arrays 输出的Arrow数组列表
     * @return 成功返回true，失败返回false
     */
    bool buildArrowArrays(const std::vector<FileSystemInfoDuck>& fileInfos,
                         int64_t startId,
                         std::vector<std::shared_ptr<arrow::Array>>& arrays);

    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);

private:
    std::shared_ptr<arrow::Schema> m_schema;           // Arrow Schema
    std::string m_lastError;                           // 最后的错误信息
    PerformanceStats m_stats;                          // 性能统计
    
    // Parquet写入选项
    arrow::Compression::type m_compressionType;       // 压缩类型
    int64_t m_chunkSize;                              // 块大小
    
    // Arrow内存池
    arrow::MemoryPool* m_memoryPool;                  // 内存池
};

#endif // ARROWMFTWRITER_H
