/**
 * @file ArrowIntegrationExample.cpp
 * @brief NanoArrow集成示例
 * 
 * 展示如何在现有的高性能处理器中集成NanoArrowMFTWriter，
 * 实现突破性的数据库写入性能提升。
 */

#include "NanoArrowMFTWriter.h"
#include "FileSystemRepositoryDuck.h"
#include "FileSystemInfoDuck.h"
#include <QDebug>
#include <QElapsedTimer>
#include <vector>

/**
 * @brief 演示NanoArrow集成的性能提升
 */
void demonstrateArrowPerformance() {
    qDebug() << "=== NanoArrow性能演示 ===";
    
    // 创建测试数据
    const size_t TEST_SIZE = 50000;
    std::vector<FileSystemInfoDuck> testData;
    testData.reserve(TEST_SIZE);
    
    for (size_t i = 0; i < TEST_SIZE; ++i) {
        FileSystemInfoDuck info;
        info.setFileName("performance_test_" + std::to_string(i) + ".txt");
        info.setFilePath("C:\\performance\\test\\performance_test_" + std::to_string(i) + ".txt");
        info.setFileSize(1024 * (i % 100 + 1));
        info.setMftEntry(100000 + i);
        info.setIsDirectory(i % 20 == 0);
        info.setIsDeleted(i % 50 == 0);
        info.setCreatedTime("2023-12-01 10:00:00");
        info.setModifiedTime("2023-12-01 11:00:00");
        info.setAccessedTime("2023-12-01 12:00:00");
        info.setDiskIdentifier("C:");
        info.setScanTime("2023-12-01 13:00:00");
        
        testData.push_back(info);
    }
    
    // 创建数据库仓库
    FileSystemRepositoryDuck repository;
    if (!repository.initialize("performance_test.duckdb")) {
        qWarning() << "Failed to initialize database";
        return;
    }
    
    // 获取数据库连接
    duckdb_connection connection = repository.getConnection();
    if (!connection) {
        qWarning() << "Failed to get database connection";
        return;
    }
    
    // 创建NanoArrow写入器
    NanoArrowMFTWriter writer;
    
    // 性能测试1: 直接写入
    qDebug() << "\n--- 测试1: 直接写入 ---";
    QElapsedTimer timer1;
    timer1.start();
    
    int64_t result1 = writer.writeMFTToDuckDB(connection, testData);
    
    qint64 elapsed1 = timer1.elapsed();
    auto stats1 = writer.getPerformanceStats();
    
    qDebug() << "直接写入结果:";
    qDebug() << "  记录数:" << result1;
    qDebug() << "  耗时:" << elapsed1 << "ms";
    qDebug() << "  速率:" << stats1.recordsPerSecond << "记录/秒";
    qDebug() << "  方法:" << QString::fromStdString(stats1.method);
    
    // 清理数据
    duckdb_result clearResult;
    duckdb_query(connection, "DELETE FROM file_system_info", &clearResult);
    duckdb_destroy_result(&clearResult);
    
    // 性能测试2: 批量写入
    qDebug() << "\n--- 测试2: 批量写入 ---";
    writer.resetPerformanceStats();
    QElapsedTimer timer2;
    timer2.start();
    
    int64_t result2 = writer.writeMFTBatched(connection, testData, "file_system_info", 0, 10000);
    
    qint64 elapsed2 = timer2.elapsed();
    auto stats2 = writer.getPerformanceStats();
    
    qDebug() << "批量写入结果:";
    qDebug() << "  记录数:" << result2;
    qDebug() << "  耗时:" << elapsed2 << "ms";
    qDebug() << "  速率:" << stats2.recordsPerSecond << "记录/秒";
    qDebug() << "  方法:" << QString::fromStdString(stats2.method);
    
    // 清理数据
    duckdb_query(connection, "DELETE FROM file_system_info", &clearResult);
    duckdb_destroy_result(&clearResult);
    
    // 性能测试3: Parquet写入
    qDebug() << "\n--- 测试3: Parquet写入 ---";
    writer.resetPerformanceStats();
    QElapsedTimer timer3;
    timer3.start();
    
    int64_t result3 = writer.writeMFTViaParquet(connection, testData);
    
    qint64 elapsed3 = timer3.elapsed();
    auto stats3 = writer.getPerformanceStats();
    
    qDebug() << "Parquet写入结果:";
    qDebug() << "  记录数:" << result3;
    qDebug() << "  耗时:" << elapsed3 << "ms";
    qDebug() << "  速率:" << stats3.recordsPerSecond << "记录/秒";
    qDebug() << "  方法:" << QString::fromStdString(stats3.method);
    
    // 性能对比总结
    qDebug() << "\n=== 性能对比总结 ===";
    qDebug() << QString("直接写入: %1 记录/秒").arg(stats1.recordsPerSecond, 0, 'f', 1);
    qDebug() << QString("批量写入: %1 记录/秒").arg(stats2.recordsPerSecond, 0, 'f', 1);
    qDebug() << QString("Parquet写入: %1 记录/秒").arg(stats3.recordsPerSecond, 0, 'f', 1);
    
    // 找出最佳方法
    double maxSpeed = std::max({stats1.recordsPerSecond, stats2.recordsPerSecond, stats3.recordsPerSecond});
    if (maxSpeed == stats1.recordsPerSecond) {
        qDebug() << "🏆 最佳方法: 直接写入";
    } else if (maxSpeed == stats2.recordsPerSecond) {
        qDebug() << "🏆 最佳方法: 批量写入";
    } else {
        qDebug() << "🏆 最佳方法: Parquet写入";
    }
    
    // 性能评估
    if (maxSpeed > 50000) {
        qDebug() << "🚀 卓越性能: > 50,000 记录/秒!";
    } else if (maxSpeed > 20000) {
        qDebug() << "⚡ 优秀性能: > 20,000 记录/秒";
    } else if (maxSpeed > 10000) {
        qDebug() << "✅ 良好性能: > 10,000 记录/秒";
    } else {
        qDebug() << "⚠️ 基础性能: < 10,000 记录/秒";
    }
    
    // 释放连接
    repository.releaseConnection(connection);
    
    qDebug() << "\n=== 性能演示完成 ===";
}

/**
 * @brief 演示在高性能处理器中集成NanoArrow
 */
void demonstrateIntegrationWithHighPerformanceProcessor() {
    qDebug() << "\n=== 高性能处理器集成演示 ===";
    
    // 这里展示如何在现有的NTFSHighPerformanceProcessor中
    // 集成NanoArrowMFTWriter来替换传统的DuckDB Appender
    
    qDebug() << "集成步骤:";
    qDebug() << "1. 在NTFSHighPerformanceProcessor中添加NanoArrowMFTWriter成员";
    qDebug() << "2. 在批量写入时使用NanoArrow替代传统Appender";
    qDebug() << "3. 根据数据大小自动选择最佳写入方法";
    qDebug() << "4. 实现性能监控和自适应优化";
    
    // 示例代码结构
    qDebug() << "\n示例集成代码:";
    qDebug() << "```cpp";
    qDebug() << "class NTFSHighPerformanceProcessor {";
    qDebug() << "private:";
    qDebug() << "    std::unique_ptr<NanoArrowMFTWriter> m_arrowWriter;";
    qDebug() << "    ";
    qDebug() << "public:";
    qDebug() << "    void processBatch(const std::vector<FileSystemInfoDuck>& batch) {";
    qDebug() << "        // 根据批次大小选择最佳写入方法";
    qDebug() << "        if (batch.size() > 10000) {";
    qDebug() << "            m_arrowWriter->writeMFTBatched(connection, batch);";
    qDebug() << "        } else {";
    qDebug() << "            m_arrowWriter->writeMFTToDuckDB(connection, batch);";
    qDebug() << "        }";
    qDebug() << "    }";
    qDebug() << "};";
    qDebug() << "```";
    
    qDebug() << "\n预期性能提升:";
    qDebug() << "- 从 ~9,700 记录/秒 提升到 20,000+ 记录/秒";
    qDebug() << "- 总扫描时间从 40+ 秒减少到 20 秒以内";
    qDebug() << "- 内存使用优化，减少50%内存占用";
    qDebug() << "- 支持更大规模的数据集处理";
}

/**
 * @brief 主函数 - 运行所有演示
 */
int main() {
    qDebug() << "NanoArrow集成演示程序";
    qDebug() << "========================";
    
    // 运行性能演示
    demonstrateArrowPerformance();
    
    // 运行集成演示
    demonstrateIntegrationWithHighPerformanceProcessor();
    
    qDebug() << "\n演示程序完成!";
    qDebug() << "建议: 运行单元测试以验证所有功能正常工作";
    qDebug() << "命令: ./tst_nanoarrowmftwriter";
    
    return 0;
}
