/**
 * @file NanoArrowMFTWriter.h
 * @brief 基于nanoarrow的轻量级MFT数据写入器
 * 
 * 该类使用DuckDB内置的nanoarrow支持，实现高性能的MFT数据写入，
 * 避免了完整Apache Arrow库的复杂依赖，同时获得更好的性能。
 */

#ifndef NANOARROWMFTWRITER_H
#define NANOARROWMFTWRITER_H

#include <memory>
#include <vector>
#include <string>

// DuckDB headers
#include <duckdb.h>

// NanoArrow headers (根据配置选择)
#ifdef HAVE_NANOARROW_STANDALONE
#include <nanoarrow/nanoarrow.h>
#endif

// Project headers
#include "FileSystemInfoDuck.h"

/**
 * @class NanoArrowMFTWriter
 * @brief 轻量级高性能MFT数据写入器，优先使用独立nanoarrow，回退到DuckDB内置支持
 *
 * 优势：
 * 1. 轻量级 - 使用nanoarrow而非完整Apache Arrow库
 * 2. 高性能 - 直接使用优化的Arrow接口
 * 3. 灵活集成 - 支持独立nanoarrow和DuckDB内置支持
 * 4. 内存高效 - 流式处理，减少内存占用
 * 5. 自适应 - 根据可用库自动选择最佳实现
 */
class NanoArrowMFTWriter {
public:
    /**
     * @brief 构造函数
     */
    NanoArrowMFTWriter();
    
    /**
     * @brief 析构函数
     */
    ~NanoArrowMFTWriter();

    /**
     * @brief 直接将MFT数据写入DuckDB（超高性能）
     * @param connection DuckDB连接
     * @param fileInfos MFT文件信息列表
     * @param tableName 目标表名
     * @param startId 起始ID
     * @return 写入的记录数，失败返回-1
     */
    int64_t writeMFTToDuckDB(duckdb_connection connection,
                            const std::vector<FileSystemInfoDuck>& fileInfos,
                            const std::string& tableName = "file_system_info",
                            int64_t startId = 0);

    /**
     * @brief 批量写入MFT数据（分批处理大数据集）
     * @param connection DuckDB连接
     * @param fileInfos MFT文件信息列表
     * @param tableName 目标表名
     * @param startId 起始ID
     * @param batchSize 批次大小（默认50000）
     * @return 写入的记录数，失败返回-1
     */
    int64_t writeMFTBatched(duckdb_connection connection,
                           const std::vector<FileSystemInfoDuck>& fileInfos,
                           const std::string& tableName = "file_system_info",
                           int64_t startId = 0,
                           size_t batchSize = 50000);

    /**
     * @brief 创建临时Parquet文件并导入DuckDB
     * @param connection DuckDB连接
     * @param fileInfos MFT文件信息列表
     * @param tableName 目标表名
     * @param startId 起始ID
     * @return 写入的记录数，失败返回-1
     */
    int64_t writeMFTViaParquet(duckdb_connection connection,
                              const std::vector<FileSystemInfoDuck>& fileInfos,
                              const std::string& tableName = "file_system_info",
                              int64_t startId = 0);

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息字符串
     */
    std::string getLastError() const;

    /**
     * @brief 获取性能统计信息
     */
    struct PerformanceStats {
        int64_t recordsWritten = 0;
        int64_t writeTimeMs = 0;
        double recordsPerSecond = 0.0;
        size_t memoryUsedBytes = 0;
        std::string method; // "direct", "batched", "parquet"
    };

    /**
     * @brief 获取性能统计
     * @return 性能统计结构
     */
    PerformanceStats getPerformanceStats() const;

    /**
     * @brief 重置性能统计
     */
    void resetPerformanceStats();

    /**
     * @brief 设置写入选项
     * @param enableCompression 是否启用压缩
     * @param batchSize 批次大小
     */
    void setWriteOptions(bool enableCompression = true, size_t batchSize = 50000);

private:
    /**
     * @brief 创建Arrow Schema的SQL定义
     * @return SQL CREATE TABLE语句
     */
    std::string createArrowSchemaSQL(const std::string& tableName);

    /**
     * @brief 将MFT数据转换为Arrow流
     * @param fileInfos MFT文件信息列表
     * @param startId 起始ID
     * @param stream 输出的Arrow流
     * @return 成功返回true，失败返回false
     */
    bool convertMFTToArrowStream(const std::vector<FileSystemInfoDuck>& fileInfos,
                                int64_t startId,
                                duckdb_arrow_stream* stream);

    /**
     * @brief 执行SQL语句
     * @param connection DuckDB连接
     * @param sql SQL语句
     * @return 成功返回true，失败返回false
     */
    bool executeSql(duckdb_connection connection, const std::string& sql);

    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);

    /**
     * @brief 生成临时文件名
     * @return 临时文件路径
     */
    std::string generateTempFileName();

private:
    std::string m_lastError;           // 最后的错误信息
    PerformanceStats m_stats;          // 性能统计
    bool m_enableCompression;          // 是否启用压缩
    size_t m_batchSize;               // 批次大小
};

#endif // NANOARROWMFTWRITER_H
