#ifndef NTFSHIGHPERFORMANCEPROCESSOR_H
#define NTFSHIGHPERFORMANCEPROCESSOR_H

#include <QObject>
#include <QTimer>
#include <memory>
#include <vector>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <thread>
#include <chrono>
#include <unordered_map>
#include "WindowsNTFSManagerDuck.h"

// 前向声明
class FileSystemRepositoryDuck;
class FileSystemInfoDuck;
class NTFSColumnBuffer;

/**
 * @brief NTFS高性能处理器
 *
 * 实现生产者-消费者模式的高性能NTFS扫描：
 * [生产者线程池] (从MFT读取原始数据)
 *       ↓
 * [原始数据队列] (线程安全队列)
 *       ↓
 * [消费者线程池] (SIMD解析数据并填充列式内存缓冲)
 *       ↓
 * [列缓冲管理器] (管理多个列缓冲，当达到阈值时触发写入)
 *       ↓
 * [DuckDB批量写入] (使用Appender写入)
 */
class NTFSHighPerformanceProcessor : public QObject {
    Q_OBJECT

public:
    /**
     * @brief 原始MFT数据结构
     */
    struct RawMFTData {
        std::vector<WindowsNTFSManagerDuck::MFTRecordInfo> mftRecords;  ///< MFT记录向量
        uint32_t size;                      ///< 记录数量
        uint64_t startEntry;                ///< 起始MFT条目号
        uint64_t entryCount;                ///< 条目数量
        std::string diskIdentifier;         ///< 磁盘标识符

        RawMFTData() : size(0), startEntry(0), entryCount(0) {}

        // 拷贝构造函数
        RawMFTData(const RawMFTData& other)
            : mftRecords(other.mftRecords)
            , size(other.size)
            , startEntry(other.startEntry)
            , entryCount(other.entryCount)
            , diskIdentifier(other.diskIdentifier) {}

        // 移动构造函数
        RawMFTData(RawMFTData&& other) noexcept
            : mftRecords(std::move(other.mftRecords))
            , size(other.size)
            , startEntry(other.startEntry)
            , entryCount(other.entryCount)
            , diskIdentifier(std::move(other.diskIdentifier)) {}

        // 拷贝赋值操作符
        RawMFTData& operator=(const RawMFTData& other) {
            if (this != &other) {
                mftRecords = other.mftRecords;
                size = other.size;
                startEntry = other.startEntry;
                entryCount = other.entryCount;
                diskIdentifier = other.diskIdentifier;
            }
            return *this;
        }

        // 移动赋值操作符
        RawMFTData& operator=(RawMFTData&& other) noexcept {
            if (this != &other) {
                mftRecords = std::move(other.mftRecords);
                size = other.size;
                startEntry = other.startEntry;
                entryCount = other.entryCount;
                diskIdentifier = std::move(other.diskIdentifier);
            }
            return *this;
        }
    };

    /**
     * @brief 性能统计信息
     */
    struct PerformanceStats {
        uint64_t totalProduced;     ///< 生产的总数据量
        uint64_t totalParsed;       ///< 解析的总记录数
        uint64_t totalStored;       ///< 存储的总记录数
        uint64_t queueSize;         ///< 当前队列大小
        uint64_t bufferFlushes;     ///< 缓冲区刷新次数
        double parseRate;           ///< 解析速率（记录/秒）
        double storeRate;           ///< 存储速率（记录/秒）

        PerformanceStats() : totalProduced(0), totalParsed(0), totalStored(0),
                           queueSize(0), bufferFlushes(0), parseRate(0.0), storeRate(0.0) {}
    };

public:
    /**
     * @brief 构造函数
     * @param repository 文件系统仓库
     * @param parent 父对象
     */
    explicit NTFSHighPerformanceProcessor(FileSystemRepositoryDuck* repository, QObject* parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~NTFSHighPerformanceProcessor() override;

    /**
     * @brief 配置处理器
     * @param producerThreads 生产者线程数
     * @param consumerThreads 消费者线程数
     * @param queueSize 队列大小
     * @param bufferSize 缓冲区大小
     * @param useSIMD 是否使用SIMD
     * @param dbWriteThreads 数据库写入线程数
     * @param testMode 测试模式（跳过数据库操作）
     */
    void configure(int producerThreads, int consumerThreads, int queueSize,
                   int bufferSize, bool useSIMD, int dbWriteThreads, bool testMode = false);

    /**
     * @brief 开始处理
     * @param ntfsManager NTFS管理器
     * @param startEntry 起始MFT条目
     * @param maxEntries 最大条目数
     * @param diskIdentifier 磁盘标识符
     * @return 成功返回true
     */
    bool startProcessing(WindowsNTFSManagerDuck* ntfsManager, uint64_t startEntry, 
                        uint64_t maxEntries, const std::string& diskIdentifier);

    /**
     * @brief 停止处理
     */
    void stopProcessing();

    /**
     * @brief 获取性能统计信息
     * @return 性能统计
     */
    PerformanceStats getPerformanceStats() const;

    /**
     * @brief 检查是否正在处理
     * @return 正在处理返回true
     */
    bool isProcessing() const { return m_processing; }

    /**
     * @brief 等待处理完成
     * @param timeoutMs 超时时间（毫秒）
     * @return 成功完成返回true，超时返回false
     */
    bool waitForCompletion(int timeoutMs = 300000); // 默认5分钟超时

    // MFT解析优化函数（用于测试和高级用法）

    /**
     * @brief SIMD优化的MFT记录解析
     * @param buffer 数据缓冲区
     * @param size 缓冲区大小
     * @param startEntry 起始条目号
     * @param entryCount 条目数量
     * @param fileInfos 输出的文件信息列表
     * @param diskIdentifier 磁盘标识符
     * @return 解析的记录数
     */
    uint64_t parseMFTRecordsSIMD(const char* buffer, uint32_t size, uint64_t startEntry,
                                uint64_t entryCount, std::vector<FileSystemInfoDuck>& fileInfos,
                                const std::string& diskIdentifier);

    /**
     * @brief 超高性能MFT记录解析（使用向量化和内存池）
     * @param buffer 数据缓冲区
     * @param size 缓冲区大小
     * @param startEntry 起始条目号
     * @param entryCount 条目数量
     * @param fileInfos 输出的文件信息列表
     * @param diskIdentifier 磁盘标识符
     * @return 解析的记录数
     */
    uint64_t parseMFTRecordsUltraFast(const char* buffer, uint32_t size, uint64_t startEntry,
                                     uint64_t entryCount, std::vector<FileSystemInfoDuck>& fileInfos,
                                     const std::string& diskIdentifier);

    /**
     * @brief 批量验证MFT记录签名（SIMD优化）
     * @param buffer 数据缓冲区
     * @param recordCount 记录数量
     * @param validMask 输出的有效性掩码
     * @return 有效记录数量
     */
    uint64_t validateMFTRecordsSIMD(const char* buffer, uint64_t recordCount, std::vector<bool>& validMask);

    /**
     * @brief 批量提取文件名（SIMD优化字符串处理）
     * @param records MFT记录数组
     * @param recordCount 记录数量
     * @param fileNames 输出的文件名数组
     * @return 成功提取的文件名数量
     */
    uint64_t extractFileNamesBatch(const WindowsNTFSManagerDuck::MFTRecordInfo* records,
                                  uint64_t recordCount, std::vector<std::string>& fileNames);

    /**
     * @brief 批量时间戳格式化（缓存优化）
     * @param timestamps 时间戳数组
     * @param count 时间戳数量
     * @param formattedTimes 输出的格式化时间数组
     * @return 成功格式化的时间戳数量
     */
    uint64_t formatTimestampsBatch(const uint64_t* timestamps, uint64_t count,
                                  std::vector<std::string>& formattedTimes);

    /**
     * @brief 优化的直接MFT记录解析
     * @param records MFT记录数组
     * @param recordCount 记录数量
     * @param startEntry 起始条目号
     * @param diskIdentifier 磁盘标识符
     * @param fileInfos 输出的文件信息列表
     * @return 解析的记录数
     */
    uint64_t parseMFTRecordsDirectOptimized(const WindowsNTFSManagerDuck::MFTRecordInfo* records,
                                           uint64_t recordCount, uint64_t startEntry,
                                           const std::string& diskIdentifier,
                                           std::vector<FileSystemInfoDuck>& fileInfos);

signals:
    /**
     * @brief 处理开始信号
     */
    void processingStarted();

    /**
     * @brief 处理完成信号
     * @param totalRecords 总记录数
     * @param elapsedMs 耗时（毫秒）
     */
    void processingCompleted(uint64_t totalRecords, uint64_t elapsedMs);

    /**
     * @brief 进度更新信号
     * @param processed 已处理数量
     * @param total 总数量
     * @param rate 处理速率（记录/秒）
     */
    void progressUpdated(uint64_t processed, uint64_t total, double rate);

    /**
     * @brief 错误信号
     * @param errorMessage 错误信息
     */
    void errorOccurred(const QString& errorMessage);

private:
    FileSystemRepositoryDuck* m_repository;         ///< 文件系统仓库
    std::unique_ptr<NTFSColumnBuffer> m_columnBuffer; ///< 列缓冲管理器
    
    // 配置参数
    int m_producerThreads;                          ///< 生产者线程数
    int m_consumerThreads;                          ///< 消费者线程数
    int m_queueSize;                                ///< 队列大小
    int m_bufferSize;                               ///< 缓冲区大小
    bool m_useSIMD;                                 ///< 是否使用SIMD
    int m_dbWriteThreads;                           ///< 数据库写入线程数
    bool m_testMode;                                ///< 测试模式（跳过数据库操作）
    
    // 线程安全队列
    std::queue<RawMFTData> m_rawDataQueue;          ///< 原始数据队列
    mutable std::mutex m_queueMutex;                ///< 队列互斥锁
    std::condition_variable m_queueCondition;       ///< 队列条件变量
    
    // 线程管理
    std::vector<std::thread> m_producerThreadPool;  ///< 生产者线程池
    std::vector<std::thread> m_consumerThreadPool;  ///< 消费者线程池
    std::atomic<bool> m_stopRequested;              ///< 停止请求标志
    std::atomic<bool> m_processing;                 ///< 处理状态标志
    std::atomic<bool> m_monitorCompleted;           ///< 监控完成标志
    
    // 统计信息
    std::atomic<uint64_t> m_globalProducedCount;    ///< 全局生产计数
    std::atomic<uint64_t> m_globalParsedCount;      ///< 全局解析计数
    std::atomic<int> m_activeParsers;               ///< 活跃解析器数量
    uint64_t m_maxEntries;                          ///< 最大条目数
    
    // 性能统计（使用原子类型保证线程安全）
    std::atomic<uint64_t> m_totalProduced;          ///< 生产的总数据量
    std::atomic<uint64_t> m_totalParsed;            ///< 解析的总记录数
    std::atomic<uint64_t> m_totalStored;            ///< 存储的总记录数
    std::atomic<uint64_t> m_currentQueueSize;       ///< 当前队列大小
    std::atomic<uint64_t> m_bufferFlushes;          ///< 缓冲区刷新次数
    std::atomic<double> m_parseRate;                ///< 解析速率（记录/秒）
    std::atomic<double> m_storeRate;                ///< 存储速率（记录/秒）
    QTimer* m_statsTimer;                           ///< 统计定时器
    std::chrono::steady_clock::time_point m_startTime; ///< 处理开始时间
    
    /**
     * @brief 生产者线程函数
     * @param ntfsManager NTFS管理器
     * @param startEntry 起始MFT条目
     * @param maxEntries 最大条目数
     * @param diskIdentifier 磁盘标识符
     */
    void producerThreadFunc(WindowsNTFSManagerDuck* ntfsManager, uint64_t startEntry, 
                           uint64_t maxEntries, const std::string& diskIdentifier);

    /**
     * @brief 消费者线程函数
     */
    void consumerThreadFunc();

    /**
     * @brief 将原始数据添加到队列
     * @param rawData 原始数据
     * @return 成功返回true
     */
    bool enqueueRawData(const RawMFTData& rawData);

    /**
     * @brief 从队列获取原始数据
     * @param rawData 输出的原始数据
     * @return 成功返回true
     */
    bool dequeueRawData(RawMFTData& rawData);

    /**
     * @brief 解析原始MFT数据（支持SIMD优化）
     * @param rawData 原始数据
     * @param fileInfos 输出的文件信息列表
     * @return 解析的记录数
     */
    uint64_t parseRawMFTData(const RawMFTData& rawData, std::vector<FileSystemInfoDuck>& fileInfos);



    /**
     * @brief 标准MFT记录解析
     * @param buffer 数据缓冲区
     * @param size 缓冲区大小
     * @param startEntry 起始条目号
     * @param entryCount 条目数量
     * @param fileInfos 输出的文件信息列表
     * @param diskIdentifier 磁盘标识符
     * @return 解析的记录数
     */
    uint64_t parseMFTRecordsStandard(const char* buffer, uint32_t size, uint64_t startEntry,
                                    uint64_t entryCount, std::vector<FileSystemInfoDuck>& fileInfos,
                                    const std::string& diskIdentifier);

    /**
     * @brief 直接解析MFT记录（避免内存复制）
     * @param mftRecords MFT记录向量
     * @param startEntry 起始条目号
     * @param diskIdentifier 磁盘标识符
     * @param fileInfos 输出的文件信息列表
     * @return 解析的记录数
     */
    uint64_t parseMFTRecordsDirect(const std::vector<WindowsNTFSManagerDuck::MFTRecordInfo>& mftRecords,
                                 uint64_t startEntry, const std::string& diskIdentifier,
                                 std::vector<FileSystemInfoDuck>& fileInfos);

    /**
     * @brief 更新性能统计
     */
    void updatePerformanceStats();

    /**
     * @brief 监控线程函数
     */
    void monitorThreadFunc();

private:
    // 优化相关的缓存和内存池
    mutable std::unordered_map<uint64_t, std::string> m_timestampCache;  // 时间戳格式化缓存
    mutable std::mutex m_timestampCacheMutex;                            // 时间戳缓存互斥锁

    // SIMD对齐的工作缓冲区
    alignas(32) mutable char m_simdWorkBuffer[32768];  // 32KB SIMD对齐缓冲区

    // 字符串转换优化
    mutable std::vector<char> m_utf8ConversionBuffer;     // UTF-8转换缓冲区
    mutable std::vector<wchar_t> m_wcharConversionBuffer; // 宽字符转换缓冲区
};

#endif // NTFSHIGHPERFORMANCEPROCESSOR_H
