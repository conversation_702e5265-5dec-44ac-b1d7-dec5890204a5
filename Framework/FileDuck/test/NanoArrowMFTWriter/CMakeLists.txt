# NanoArrowMFTWriter 测试模块

# 创建测试可执行文件
add_executable(tst_nanoarrowmftwriter
    tst_nanoarrowmftwriter.cpp
)

# 链接必要的库
target_link_libraries(tst_nanoarrowmftwriter
    Qt6::Test
    Qt6::Core
    FileDuck
    DatabaseDuck
    Arrow  # NanoArrow支持
)

# 设置测试属性
set_target_properties(tst_nanoarrowmftwriter PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests
)

# 添加到测试套件
add_test(NAME NanoArrowMFTWriterTest COMMAND tst_nanoarrowmftwriter)

# 设置测试环境
set_tests_properties(NanoArrowMFTWriterTest PROPERTIES
    TIMEOUT 300  # 5分钟超时
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/tests
)
