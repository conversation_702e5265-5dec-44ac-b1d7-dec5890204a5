/**
 * @file tst_nanoarrowmftwriter.cpp
 * @brief NanoArrowMFTWriter单元测试
 * 
 * 测试基于nanoarrow的轻量级MFT数据写入器的各项功能，
 * 包括性能测试、数据完整性验证和错误处理。
 */

#include <QtTest>
#include <QCoreApplication>
#include <QTemporaryDir>
#include <QSignalSpy>
#include <QEventLoop>
#include <QTimer>
#include <QDebug>
#include <QElapsedTimer>
#include <memory>
#include <random>

// 包含需要测试的类
#include "NanoArrowMFTWriter.h"
#include "FileSystemRepositoryDuck.h"
#include "DatabaseManagerDuck.h"
#include "FileSystemInfoDuck.h"

class NanoArrowMFTWriterTest : public QObject {
    Q_OBJECT

public:
    NanoArrowMFTWriterTest();

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 基础功能测试
    void testWriterCreation();
    void testWriteOptionsConfiguration();
    void testErrorHandling();
    
    // 数据写入测试
    void testSmallDatasetWrite();
    void testLargeDatasetWrite();
    void testBatchedWrite();
    void testParquetWrite();
    
    // 性能测试
    void testWritePerformance();
    void testMemoryUsage();
    void testCompressionEfficiency();
    
    // 数据完整性测试
    void testDataIntegrity();
    void testSpecialCharacters();
    void testEmptyFields();
    
    // 集成测试
    void testDuckDBIntegration();
    void testConcurrentWrites();

private:
    // 辅助方法
    void waitForDatabaseReady();
    std::vector<FileSystemInfoDuck> generateTestData(size_t count);
    bool verifyDataInDatabase(duckdb_connection connection, 
                             const std::vector<FileSystemInfoDuck>& expectedData,
                             int64_t startId = 0);

private:
    std::unique_ptr<QTemporaryDir> m_tempDir;
    std::unique_ptr<FileSystemRepositoryDuck> m_repository;
    std::unique_ptr<NanoArrowMFTWriter> m_writer;
    QString m_testDbPath;
};

NanoArrowMFTWriterTest::NanoArrowMFTWriterTest() {
}

void NanoArrowMFTWriterTest::initTestCase() {
    qDebug() << "=== NanoArrowMFTWriter Test Suite Started ===";
    
    // 创建临时目录
    m_tempDir = std::make_unique<QTemporaryDir>();
    QVERIFY(m_tempDir->isValid());
    
    m_testDbPath = m_tempDir->path() + "/test_nanoarrow.duckdb";
    qDebug() << "Test database path:" << m_testDbPath;
    
    // 创建数据库仓库
    m_repository = std::make_unique<FileSystemRepositoryDuck>();
    QVERIFY(m_repository != nullptr);
    
    // 初始化数据库
    bool dbInitialized = m_repository->initialize(m_testDbPath.toStdString());
    QVERIFY(dbInitialized);
    
    // 等待数据库准备就绪
    waitForDatabaseReady();
    
    qDebug() << "Test setup completed";
}

void NanoArrowMFTWriterTest::cleanupTestCase() {
    qDebug() << "=== NanoArrowMFTWriter Test Suite Completed ===";
    
    // 清理资源
    m_writer.reset();
    m_repository.reset();
    m_tempDir.reset();
}

void NanoArrowMFTWriterTest::init() {
    // 每个测试前创建新的writer
    m_writer = std::make_unique<NanoArrowMFTWriter>();
    QVERIFY(m_writer != nullptr);
}

void NanoArrowMFTWriterTest::cleanup() {
    // 每个测试后清理
    if (m_writer) {
        m_writer.reset();
    }
}

void NanoArrowMFTWriterTest::testWriterCreation() {
    qDebug() << "\n--- 测试Writer创建 ---";
    
    // 测试默认构造
    auto writer = std::make_unique<NanoArrowMFTWriter>();
    QVERIFY(writer != nullptr);
    
    // 测试初始状态
    auto stats = writer->getPerformanceStats();
    QCOMPARE(stats.recordsWritten, 0);
    QCOMPARE(stats.writeTimeMs, 0);
    QVERIFY(stats.method.empty());
    
    // 测试错误状态
    QVERIFY(writer->getLastError().empty());
    
    qDebug() << "✓ Writer创建测试通过";
}

void NanoArrowMFTWriterTest::testWriteOptionsConfiguration() {
    qDebug() << "\n--- 测试写入选项配置 ---";
    
    // 测试默认选项
    auto stats1 = m_writer->getPerformanceStats();
    
    // 测试设置选项
    m_writer->setWriteOptions(true, 25000);  // 启用压缩，批次大小25000
    
    // 重置统计
    m_writer->resetPerformanceStats();
    auto stats2 = m_writer->getPerformanceStats();
    QCOMPARE(stats2.recordsWritten, 0);
    
    qDebug() << "✓ 写入选项配置测试通过";
}

void NanoArrowMFTWriterTest::testErrorHandling() {
    qDebug() << "\n--- 测试错误处理 ---";
    
    // 测试无效连接
    std::vector<FileSystemInfoDuck> testData = generateTestData(10);
    int64_t result = m_writer->writeMFTToDuckDB(nullptr, testData);
    QCOMPARE(result, -1);
    QVERIFY(!m_writer->getLastError().empty());
    
    // 测试空数据
    duckdb_connection connection = m_repository->getConnection();
    std::vector<FileSystemInfoDuck> emptyData;
    result = m_writer->writeMFTToDuckDB(connection, emptyData);
    QCOMPARE(result, -1);
    
    m_repository->releaseConnection(connection);
    
    qDebug() << "✓ 错误处理测试通过";
}

void NanoArrowMFTWriterTest::testSmallDatasetWrite() {
    qDebug() << "\n--- 测试小数据集写入 ---";
    
    // 生成测试数据
    const size_t TEST_SIZE = 100;
    std::vector<FileSystemInfoDuck> testData = generateTestData(TEST_SIZE);
    
    // 获取数据库连接
    duckdb_connection connection = m_repository->getConnection();
    QVERIFY(connection != nullptr);
    
    // 写入数据
    QElapsedTimer timer;
    timer.start();
    
    int64_t result = m_writer->writeMFTToDuckDB(connection, testData);
    
    qint64 elapsed = timer.elapsed();
    
    // 验证结果
    QVERIFY(result > 0);
    QCOMPARE(result, static_cast<int64_t>(TEST_SIZE));
    
    // 验证性能统计
    auto stats = m_writer->getPerformanceStats();
    QCOMPARE(stats.recordsWritten, static_cast<int64_t>(TEST_SIZE));
    QVERIFY(stats.writeTimeMs > 0);
    QVERIFY(stats.recordsPerSecond > 0);
    
    qDebug() << "写入记录数:" << result;
    qDebug() << "耗时:" << elapsed << "ms";
    qDebug() << "写入速率:" << stats.recordsPerSecond << "记录/秒";
    qDebug() << "使用方法:" << QString::fromStdString(stats.method);
    
    // 验证数据完整性
    QVERIFY(verifyDataInDatabase(connection, testData));
    
    m_repository->releaseConnection(connection);
    
    qDebug() << "✓ 小数据集写入测试通过";
}

void NanoArrowMFTWriterTest::testLargeDatasetWrite() {
    qDebug() << "\n--- 测试大数据集写入 ---";
    
    // 生成大量测试数据
    const size_t TEST_SIZE = 10000;
    std::vector<FileSystemInfoDuck> testData = generateTestData(TEST_SIZE);
    
    // 获取数据库连接
    duckdb_connection connection = m_repository->getConnection();
    QVERIFY(connection != nullptr);
    
    // 写入数据
    QElapsedTimer timer;
    timer.start();
    
    int64_t result = m_writer->writeMFTToDuckDB(connection, testData);
    
    qint64 elapsed = timer.elapsed();
    
    // 验证结果
    QVERIFY(result > 0);
    QCOMPARE(result, static_cast<int64_t>(TEST_SIZE));
    
    // 验证性能统计
    auto stats = m_writer->getPerformanceStats();
    QCOMPARE(stats.recordsWritten, static_cast<int64_t>(TEST_SIZE));
    QVERIFY(stats.writeTimeMs > 0);
    QVERIFY(stats.recordsPerSecond > 0);
    
    qDebug() << "写入记录数:" << result;
    qDebug() << "耗时:" << elapsed << "ms";
    qDebug() << "写入速率:" << stats.recordsPerSecond << "记录/秒";
    qDebug() << "使用方法:" << QString::fromStdString(stats.method);
    
    // 性能要求：应该超过1000记录/秒
    QVERIFY(stats.recordsPerSecond > 1000);
    
    m_repository->releaseConnection(connection);
    
    qDebug() << "✓ 大数据集写入测试通过";
}

void NanoArrowMFTWriterTest::testBatchedWrite() {
    qDebug() << "\n--- 测试批量写入 ---";
    
    // 生成测试数据
    const size_t TEST_SIZE = 5000;
    const size_t BATCH_SIZE = 1000;
    std::vector<FileSystemInfoDuck> testData = generateTestData(TEST_SIZE);
    
    // 获取数据库连接
    duckdb_connection connection = m_repository->getConnection();
    QVERIFY(connection != nullptr);
    
    // 批量写入数据
    QElapsedTimer timer;
    timer.start();
    
    int64_t result = m_writer->writeMFTBatched(connection, testData, "file_system_info", 0, BATCH_SIZE);
    
    qint64 elapsed = timer.elapsed();
    
    // 验证结果
    QVERIFY(result > 0);
    QCOMPARE(result, static_cast<int64_t>(TEST_SIZE));
    
    // 验证性能统计
    auto stats = m_writer->getPerformanceStats();
    QCOMPARE(stats.recordsWritten, static_cast<int64_t>(TEST_SIZE));
    QCOMPARE(stats.method, "batched");
    
    qDebug() << "批量写入记录数:" << result;
    qDebug() << "耗时:" << elapsed << "ms";
    qDebug() << "写入速率:" << stats.recordsPerSecond << "记录/秒";
    qDebug() << "批次大小:" << BATCH_SIZE;
    
    m_repository->releaseConnection(connection);
    
    qDebug() << "✓ 批量写入测试通过";
}

void NanoArrowMFTWriterTest::testParquetWrite() {
    qDebug() << "\n--- 测试Parquet写入 ---";

    // 生成测试数据
    const size_t TEST_SIZE = 2000;
    std::vector<FileSystemInfoDuck> testData = generateTestData(TEST_SIZE);

    // 获取数据库连接
    duckdb_connection connection = m_repository->getConnection();
    QVERIFY(connection != nullptr);

    // 使用Parquet方式写入
    QElapsedTimer timer;
    timer.start();

    int64_t result = m_writer->writeMFTViaParquet(connection, testData);

    qint64 elapsed = timer.elapsed();

    // 验证结果
    QVERIFY(result > 0);
    QCOMPARE(result, static_cast<int64_t>(TEST_SIZE));

    // 验证性能统计
    auto stats = m_writer->getPerformanceStats();
    QCOMPARE(stats.recordsWritten, static_cast<int64_t>(TEST_SIZE));
    QCOMPARE(stats.method, "parquet");

    qDebug() << "Parquet写入记录数:" << result;
    qDebug() << "耗时:" << elapsed << "ms";
    qDebug() << "写入速率:" << stats.recordsPerSecond << "记录/秒";

    m_repository->releaseConnection(connection);

    qDebug() << "✓ Parquet写入测试通过";
}

void NanoArrowMFTWriterTest::testWritePerformance() {
    qDebug() << "\n--- 测试写入性能 ---";

    // 生成大量测试数据进行性能测试
    const size_t PERFORMANCE_TEST_SIZE = 20000;
    std::vector<FileSystemInfoDuck> testData = generateTestData(PERFORMANCE_TEST_SIZE);

    // 获取数据库连接
    duckdb_connection connection = m_repository->getConnection();
    QVERIFY(connection != nullptr);

    // 性能测试
    QElapsedTimer timer;
    timer.start();

    int64_t result = m_writer->writeMFTToDuckDB(connection, testData);

    qint64 elapsed = timer.elapsed();

    // 验证结果
    QVERIFY(result > 0);
    QCOMPARE(result, static_cast<int64_t>(PERFORMANCE_TEST_SIZE));

    // 验证性能统计
    auto stats = m_writer->getPerformanceStats();
    double recordsPerSecond = stats.recordsPerSecond;

    qDebug() << "性能测试结果:";
    qDebug() << "  记录数:" << PERFORMANCE_TEST_SIZE;
    qDebug() << "  耗时:" << elapsed << "ms";
    qDebug() << "  写入速率:" << recordsPerSecond << "记录/秒";
    qDebug() << "  使用方法:" << QString::fromStdString(stats.method);

    // 性能要求：应该超过5000记录/秒
    QVERIFY(recordsPerSecond > 5000);

    // 如果性能超过20000记录/秒，认为是优秀性能
    if (recordsPerSecond > 20000) {
        qDebug() << "  🏆 优秀性能: > 20,000 记录/秒!";
    } else if (recordsPerSecond > 10000) {
        qDebug() << "  ✓ 良好性能: > 10,000 记录/秒";
    } else {
        qDebug() << "  ⚠ 基础性能: > 5,000 记录/秒";
    }

    m_repository->releaseConnection(connection);

    qDebug() << "✓ 写入性能测试通过";
}

void NanoArrowMFTWriterTest::testDataIntegrity() {
    qDebug() << "\n--- 测试数据完整性 ---";

    // 生成包含特殊数据的测试集
    std::vector<FileSystemInfoDuck> testData;

    // 添加各种类型的测试数据
    FileSystemInfoDuck info1;
    info1.setFileName("normal_file.txt");
    info1.setFilePath("C:\\normal\\path\\normal_file.txt");
    info1.setFileSize(1024);
    info1.setMftEntry(12345);
    info1.setIsDirectory(false);
    info1.setIsDeleted(false);
    info1.setCreatedTime("2023-01-01 12:00:00");
    info1.setModifiedTime("2023-01-02 13:00:00");
    info1.setAccessedTime("2023-01-03 14:00:00");
    info1.setDiskIdentifier("C:");
    info1.setScanTime("2023-01-04 15:00:00");
    testData.push_back(info1);

    // 添加目录
    FileSystemInfoDuck info2;
    info2.setFileName("test_directory");
    info2.setFilePath("C:\\test\\test_directory");
    info2.setFileSize(0);
    info2.setMftEntry(12346);
    info2.setIsDirectory(true);
    info2.setIsDeleted(false);
    info2.setCreatedTime("2023-01-01 12:00:00");
    info2.setModifiedTime("2023-01-02 13:00:00");
    info2.setAccessedTime("2023-01-03 14:00:00");
    info2.setDiskIdentifier("C:");
    info2.setScanTime("2023-01-04 15:00:00");
    testData.push_back(info2);

    // 添加已删除文件
    FileSystemInfoDuck info3;
    info3.setFileName("deleted_file.txt");
    info3.setFilePath("C:\\deleted\\deleted_file.txt");
    info3.setFileSize(2048);
    info3.setMftEntry(12347);
    info3.setIsDirectory(false);
    info3.setIsDeleted(true);
    info3.setCreatedTime("2023-01-01 12:00:00");
    info3.setModifiedTime("2023-01-02 13:00:00");
    info3.setAccessedTime("2023-01-03 14:00:00");
    info3.setDiskIdentifier("C:");
    info3.setScanTime("2023-01-04 15:00:00");
    testData.push_back(info3);

    // 获取数据库连接
    duckdb_connection connection = m_repository->getConnection();
    QVERIFY(connection != nullptr);

    // 写入数据
    int64_t result = m_writer->writeMFTToDuckDB(connection, testData);
    QVERIFY(result > 0);
    QCOMPARE(result, static_cast<int64_t>(testData.size()));

    // 验证数据完整性
    QVERIFY(verifyDataInDatabase(connection, testData));

    m_repository->releaseConnection(connection);

    qDebug() << "✓ 数据完整性测试通过";
}

void NanoArrowMFTWriterTest::testDuckDBIntegration() {
    qDebug() << "\n--- 测试DuckDB集成 ---";

    // 生成测试数据
    const size_t TEST_SIZE = 1000;
    std::vector<FileSystemInfoDuck> testData = generateTestData(TEST_SIZE);

    // 获取数据库连接
    duckdb_connection connection = m_repository->getConnection();
    QVERIFY(connection != nullptr);

    // 写入数据
    int64_t result = m_writer->writeMFTToDuckDB(connection, testData);
    QVERIFY(result > 0);

    // 验证数据库中的记录数
    duckdb_result queryResult;
    duckdb_state state = duckdb_query(connection, "SELECT COUNT(*) FROM file_system_info", &queryResult);
    QCOMPARE(state, DuckDBSuccess);

    int64_t count = duckdb_value_int64(&queryResult, 0, 0);
    QVERIFY(count >= result);  // 可能有之前测试的数据

    duckdb_destroy_result(&queryResult);
    m_repository->releaseConnection(connection);

    qDebug() << "数据库中的记录数:" << count;
    qDebug() << "✓ DuckDB集成测试通过";
}

// 辅助方法实现
void NanoArrowMFTWriterTest::waitForDatabaseReady() {
    QEventLoop loop;
    QTimer timer;
    timer.setSingleShot(true);
    timer.setInterval(1000);  // 等待1秒

    QObject::connect(&timer, &QTimer::timeout, &loop, &QEventLoop::quit);
    timer.start();
    loop.exec();
}

std::vector<FileSystemInfoDuck> NanoArrowMFTWriterTest::generateTestData(size_t count) {
    std::vector<FileSystemInfoDuck> data;
    data.reserve(count);

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> sizeDist(0, 1000000);
    std::uniform_int_distribution<> mftDist(1000, 999999);

    for (size_t i = 0; i < count; ++i) {
        FileSystemInfoDuck info;
        info.setFileName("test_file_" + std::to_string(i) + ".txt");
        info.setFilePath("C:\\test\\path\\test_file_" + std::to_string(i) + ".txt");
        info.setFileSize(sizeDist(gen));
        info.setMftEntry(mftDist(gen));
        info.setIsDirectory(i % 10 == 0);  // 每10个文件中有1个目录
        info.setIsDeleted(i % 20 == 0);    // 每20个文件中有1个已删除
        info.setCreatedTime("2023-01-01 12:00:00");
        info.setModifiedTime("2023-01-02 13:00:00");
        info.setAccessedTime("2023-01-03 14:00:00");
        info.setDiskIdentifier("C:");
        info.setScanTime("2023-01-04 15:00:00");

        data.push_back(info);
    }

    return data;
}

bool NanoArrowMFTWriterTest::verifyDataInDatabase(duckdb_connection connection,
                                                 const std::vector<FileSystemInfoDuck>& expectedData,
                                                 int64_t startId) {
    // 简化的验证：检查记录数是否匹配
    duckdb_result result;
    duckdb_state state = duckdb_query(connection, "SELECT COUNT(*) FROM file_system_info", &result);

    if (state != DuckDBSuccess) {
        qWarning() << "Failed to query database for verification";
        duckdb_destroy_result(&result);
        return false;
    }

    int64_t count = duckdb_value_int64(&result, 0, 0);
    duckdb_destroy_result(&result);

    // 数据库中的记录数应该至少包含我们插入的数据
    return count >= static_cast<int64_t>(expectedData.size());
}

QTEST_MAIN(NanoArrowMFTWriterTest)

#include "tst_nanoarrowmftwriter.moc"
