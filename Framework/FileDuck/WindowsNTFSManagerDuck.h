#ifndef WINDOWSNTFSMANAGERDUCK_H
#define WINDOWSNTFSMANAGERDUCK_H

#include "FileSystemInfoDuck.h"
#include <QObject>
#include <QMutex>
#include <QString>
#include <string>
#include <vector>
#include <memory>
#include <functional>

#ifdef _WIN32
#include <windows.h>
#endif

/**
 * @brief Windows NTFS文件系统管理器（DuckDB版本）
 *
 * 提供Windows NTFS文件系统的底层访问功能，使用Windows原生API
 * 支持MFT（Master File Table）读取和文件信息提取
 * 返回FileSystemInfoDuck对象，适配DuckDB存储
 * 线程安全设计，支持并发访问
 */
class WindowsNTFSManagerDuck : public QObject {
    Q_OBJECT

public:
    /**
     * @brief MFT记录信息结构
     */
    struct MFTRecordInfo {
        uint64_t mftEntry;              ///< MFT条目号
        std::string fileName;           ///< 文件名
        std::string filePath;           ///< 完整路径
        uint64_t fileSize;              ///< 文件大小
        bool isDirectory;               ///< 是否为目录
        bool isDeleted;                 ///< 是否已删除
        uint64_t createdTime;           ///< 创建时间（FILETIME）
        uint64_t modifiedTime;          ///< 修改时间（FILETIME）
        uint64_t accessedTime;          ///< 访问时间（FILETIME）
    };

    /**
     * @brief 文件系统信息结构
     */
    struct FileSystemInfo {
        std::string volumeName;     ///< 卷名
        std::string fileSystem;     ///< 文件系统类型
        uint64_t totalSize;         ///< 总大小
        uint64_t freeSize;          ///< 可用大小
        uint32_t bytesPerSector;    ///< 每扇区字节数
        uint32_t sectorsPerCluster; ///< 每簇扇区数
        uint64_t totalClusters;     ///< 总簇数
        uint64_t mftStartLcn;       ///< MFT起始逻辑簇号
        uint64_t mftSize;           ///< MFT大小（条目数）
    };

    /**
     * @brief 扫描进度回调函数类型
     * @param processed 已处理的MFT条目数
     * @param total 总MFT条目数
     * @param currentFile 当前处理的文件路径
     */
    using ProgressCallback = std::function<void(uint64_t processed, uint64_t total, const std::string &currentFile)>;

    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit WindowsNTFSManagerDuck(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~WindowsNTFSManagerDuck() override;

    // 禁用拷贝构造和赋值操作
    WindowsNTFSManagerDuck(const WindowsNTFSManagerDuck &) = delete;
    WindowsNTFSManagerDuck &operator=(const WindowsNTFSManagerDuck &) = delete;

    // 支持移动构造和移动赋值
    WindowsNTFSManagerDuck(WindowsNTFSManagerDuck &&other) noexcept;
    WindowsNTFSManagerDuck &operator=(WindowsNTFSManagerDuck &&other) noexcept;

    /**
     * @brief 打开磁盘卷
     * @param driveLetter 驱动器字母（如 "C"）
     * @return 成功返回true，失败返回false
     */
    bool openVolume(const std::string &driveLetter);

    /**
     * @brief 检查是否为NTFS文件系统
     * @return 是NTFS返回true
     */
    bool isNTFS() const;

    /**
     * @brief 获取文件系统信息
     * @return 文件系统信息结构
     */
    FileSystemInfo getFileSystemInfo() const;

    /**
     * @brief 获取MFT大小（条目数）
     * @return MFT条目数量
     */
    uint64_t getMFTSize() const;

    /**
     * @brief 读取MFT记录
     * @param mftEntry MFT条目号
     * @param recordInfo 输出的记录信息
     * @return 成功返回true
     */
    bool readMFTRecord(uint64_t mftEntry, MFTRecordInfo &recordInfo);

    /**
     * @brief 批量读取MFT记录
     * @param startEntry 起始MFT条目号
     * @param count 读取数量
     * @param records 输出的记录列表
     * @param progressCallback 进度回调函数
     * @return 成功读取的记录数量
     */
    uint64_t readMFTRecords(uint64_t startEntry, uint64_t count,
                           std::vector<MFTRecordInfo> &records,
                           ProgressCallback progressCallback = nullptr);

    /**
     * @brief 批量读取MFT记录（优化版本）
     * @param startEntry 起始MFT条目号
     * @param count 读取数量
     * @param records 输出的记录列表
     * @param progressCallback 进度回调函数
     * @return 成功读取的记录数量
     */
    uint64_t readMFTRecordsBatch(uint64_t startEntry, uint64_t count,
                                std::vector<MFTRecordInfo> &records,
                                ProgressCallback progressCallback = nullptr);

    /**
     * @brief 扫描整个MFT并转换为FileSystemInfoDuck对象
     * @param fileInfos 输出的文件信息列表
     * @param progressCallback 进度回调函数
     * @return 成功扫描的文件数量
     */
    uint64_t scanMFTToFileInfos(std::vector<FileSystemInfoDuck> &fileInfos,
                               ProgressCallback progressCallback = nullptr);

    /**
     * @brief 分段扫描MFT（支持多线程）
     * @param startEntry 起始MFT条目号
     * @param endEntry 结束MFT条目号
     * @param fileInfos 输出的文件信息列表
     * @param progressCallback 进度回调函数
     * @return 成功扫描的文件数量
     */
    uint64_t scanMFTSegment(uint64_t startEntry, uint64_t endEntry,
                           std::vector<FileSystemInfoDuck> &fileInfos,
                           ProgressCallback progressCallback = nullptr);

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息字符串
     */
    std::string getLastError() const;

    /**
     * @brief 检查管理器是否已初始化
     * @return 已初始化返回true
     */
    bool isInitialized() const;

    /**
     * @brief 关闭卷并清理资源
     */
    void close();

    /**
     * @brief 重置管理器状态
     */
    void reset();

    /**
     * @brief 获取可用驱动器列表
     * @return 驱动器字母列表
     */
    static std::vector<std::string> getAvailableDrives();

    /**
     * @brief 格式化驱动器路径
     * @param driveLetter 驱动器字母
     * @return 格式化的路径
     */
    static std::string formatDrivePath(const std::string &driveLetter);

    /**
     * @brief 检查指定驱动器是否为NTFS
     * @param driveLetter 驱动器字母
     * @return 是NTFS返回true
     */
    static bool isDriveNTFS(const std::string &driveLetter);

    /**
     * @brief 将FILETIME转换为Unix时间戳
     * @param fileTime FILETIME值
     * @return Unix时间戳
     */
    static uint64_t fileTimeToUnixTime(uint64_t fileTime);

    /**
     * @brief 将MFTRecordInfo转换为FileSystemInfoDuck
     * @param recordInfo MFT记录信息
     * @param diskIdentifier 磁盘标识符
     * @return FileSystemInfoDuck对象
     */
    static FileSystemInfoDuck mftRecordToFileInfo(const MFTRecordInfo &recordInfo, const std::string &diskIdentifier);

signals:
    /**
     * @brief MFT扫描进度信号
     * @param processed 已处理数量
     * @param total 总数量
     * @param currentFile 当前文件
     */
    void scanProgress(uint64_t processed, uint64_t total, const QString &currentFile);

    /**
     * @brief MFT扫描完成信号
     * @param success 是否成功
     * @param scannedCount 扫描的文件数量
     * @param errorMessage 错误信息
     */
    void scanCompleted(bool success, uint64_t scannedCount, const QString &errorMessage);

private:
#ifdef _WIN32
    HANDLE m_volumeHandle;          ///< 卷句柄
    HANDLE m_mftHandle;             ///< MFT句柄
#endif
    std::string m_driveLetter;      ///< 驱动器字母
    std::string m_lastError;        ///< 最后的错误信息
    FileSystemInfo m_fsInfo;        ///< 文件系统信息
    mutable QMutex m_mutex;         ///< 线程安全保护

    /**
     * @brief 关闭卷句柄
     */
    void closeVolume();

    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string &error);

    /**
     * @brief 移动资源从另一个对象
     * @param other 源对象
     */
    void moveFrom(WindowsNTFSManagerDuck &&other) noexcept;

    /**
     * @brief 内部重置方法（不加锁）
     */
    void resetInternal();

    /**
     * @brief 解析MFT记录数据
     * @param recordData 记录数据
     * @param recordSize 记录大小
     * @param recordInfo 输出的记录信息
     * @return 成功返回true
     */
    bool parseMFTRecord(const void *recordData, size_t recordSize, MFTRecordInfo &recordInfo);

    /**
     * @brief 构建完整文件路径
     * @param fileName 文件名
     * @param parentMftEntry 父目录MFT条目号
     * @return 完整路径
     */
    std::string buildFullPath(const std::string &fileName, uint64_t parentMftEntry);

    /**
     * @brief 读取MFT记录（内部版本，不加锁）
     * @param mftEntry MFT条目号
     * @param recordInfo 输出的记录信息
     * @return 成功返回true
     */
    bool readMFTRecordInternal(uint64_t mftEntry, MFTRecordInfo &recordInfo);

    /**
     * @brief 读取文件系统信息
     * @return 成功返回true
     */
    bool readFileSystemInfo();

    /**
     * @brief 读取NTFS引导扇区
     * @return 成功返回true
     */
    bool readNTFSBootSector();

    /**
     * @brief 读取实际的MFT大小
     * @return 成功返回true
     */
    bool readActualMFTSize();

    /**
     * @brief 解析标准信息属性
     * @param attrData 属性数据
     * @param attrLength 属性长度
     * @param record MFT记录
     */
    void parseStandardInformation(const uint8_t *attrData, uint32_t attrLength, MFTRecordInfo &record);

    /**
     * @brief 解析文件名属性
     * @param attrData 属性数据
     * @param attrLength 属性长度
     * @param record MFT记录
     */
    void parseFileName(const uint8_t *attrData, uint32_t attrLength, MFTRecordInfo &record);

    /**
     * @brief 解析数据属性
     * @param attrData 属性数据
     * @param attrLength 属性长度
     * @param record MFT记录
     */
    void parseDataAttribute(const uint8_t *attrData, uint32_t attrLength, MFTRecordInfo &record);

    /**
     * @brief 优化版本的标准信息属性解析
     * @param attrData 属性数据
     * @param attrLength 属性长度
     * @param record MFT记录
     */
    void parseStandardInformationOptimized(const uint8_t *attrData, uint32_t attrLength, MFTRecordInfo &record);

    /**
     * @brief 优化版本的文件名属性解析
     * @param attrData 属性数据
     * @param attrLength 属性长度
     * @param record MFT记录
     */
    void parseFileNameOptimized(const uint8_t *attrData, uint32_t attrLength, MFTRecordInfo &record);

    /**
     * @brief 优化版本的数据属性解析
     * @param attrData 属性数据
     * @param attrLength 属性长度
     * @param record MFT记录
     */
    void parseDataAttributeOptimized(const uint8_t *attrData, uint32_t attrLength, MFTRecordInfo &record);

#ifdef _WIN32
    /**
     * @brief 获取Windows错误信息
     * @param errorCode 错误代码
     * @return 错误信息字符串
     */
    static std::string getWindowsErrorMessage(DWORD errorCode);
#endif
};

#endif // WINDOWSNTFSMANAGERDUCK_H
