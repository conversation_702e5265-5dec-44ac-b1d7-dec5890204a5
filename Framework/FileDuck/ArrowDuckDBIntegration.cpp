/**
 * @file ArrowDuckDBIntegration.cpp
 * @brief Apache Arrow与DuckDB集成实现
 */

#include "ArrowDuckDBIntegration.h"
#include <QDebug>
#include <QDir>
#include <QStandardPaths>
#include <QUuid>
#include <chrono>
#include <arrow/table.h>
#include <arrow/record_batch.h>
#include <arrow/io/file.h>
#include <parquet/arrow/writer.h>
#include <parquet/arrow/reader.h>

ArrowDuckDBIntegration::ArrowDuckDBIntegration() {
    resetPerformanceStats();
}

ArrowDuckDBIntegration::~ArrowDuckDBIntegration() {
    // 清理资源
}

int64_t ArrowDuckDBIntegration::importFromParquet(duckdb_connection connection,
                                                 const std::string& parquetFile,
                                                 const std::string& tableName) {
    if (!connection || parquetFile.empty()) {
        setError("Invalid connection or empty parquet file path");
        return -1;
    }

    auto start = std::chrono::high_resolution_clock::now();

    try {
        // 使用DuckDB的内置Parquet支持
        std::string sql = "INSERT INTO " + tableName + 
                         " SELECT * FROM read_parquet('" + parquetFile + "')";
        
        if (!executeSql(connection, sql)) {
            setError("Failed to import from parquet file: " + parquetFile);
            return -1;
        }

        // 获取导入的记录数
        duckdb_result result;
        std::string countSql = "SELECT COUNT(*) FROM " + tableName;
        if (duckdb_query(connection, countSql.c_str(), &result) != DuckDBSuccess) {
            setError("Failed to count imported records");
            return -1;
        }

        int64_t recordCount = 0;
        if (duckdb_row_count(&result) > 0) {
            recordCount = duckdb_value_int64(&result, 0, 0);
        }
        duckdb_destroy_result(&result);

        auto end = std::chrono::high_resolution_clock::now();
        m_stats.recordsImported = recordCount;
        m_stats.importTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        m_stats.recordsPerSecond = m_stats.importTimeMs > 0 ? 
            (m_stats.recordsImported * 1000.0) / m_stats.importTimeMs : 0;

        return recordCount;

    } catch (const std::exception& e) {
        setError("Exception in importFromParquet: " + std::string(e.what()));
        return -1;
    }
}

int64_t ArrowDuckDBIntegration::importFromArrowTable(duckdb_connection connection,
                                                    std::shared_ptr<arrow::Table> table,
                                                    const std::string& tableName) {
    if (!connection || !table) {
        setError("Invalid connection or null Arrow table");
        return -1;
    }

    auto start = std::chrono::high_resolution_clock::now();

    try {
        // 创建临时Parquet文件
        QString tempDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation);
        QString tempFile = tempDir + "/arrow_temp_" + QUuid::createUuid().toString(QUuid::WithoutBraces) + ".parquet";
        
        // 写入Arrow表到Parquet文件
        std::shared_ptr<arrow::io::FileOutputStream> outfile;
        auto result = arrow::io::FileOutputStream::Open(tempFile.toStdString());
        if (!result.ok()) {
            setError("Failed to create temp parquet file: " + result.status().ToString());
            return -1;
        }
        outfile = result.ValueOrDie();

        // 写入Parquet
        auto writeResult = parquet::arrow::WriteTable(*table, arrow::default_memory_pool(), outfile);
        if (!writeResult.ok()) {
            setError("Failed to write Arrow table to parquet: " + writeResult.ToString());
            return -1;
        }
        outfile->Close();

        // 使用Parquet导入方法
        int64_t recordCount = importFromParquet(connection, tempFile.toStdString(), tableName);

        // 清理临时文件
        QDir().remove(tempFile);

        auto end = std::chrono::high_resolution_clock::now();
        m_stats.recordsImported = recordCount;
        m_stats.importTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        m_stats.recordsPerSecond = m_stats.importTimeMs > 0 ? 
            (m_stats.recordsImported * 1000.0) / m_stats.importTimeMs : 0;

        return recordCount;

    } catch (const std::exception& e) {
        setError("Exception in importFromArrowTable: " + std::string(e.what()));
        return -1;
    }
}

int64_t ArrowDuckDBIntegration::importMFTData(duckdb_connection connection,
                                             const std::vector<FileSystemInfoDuck>& fileInfos,
                                             int64_t startId,
                                             const std::string& tableName) {
    if (!connection || fileInfos.empty()) {
        setError("Invalid connection or empty file infos");
        return -1;
    }

    auto start = std::chrono::high_resolution_clock::now();

    try {
        // 创建Arrow表
        auto table = createArrowTableFromMFTData(fileInfos, startId);
        if (!table) {
            setError("Failed to create Arrow table from MFT data");
            return -1;
        }

        // 导入Arrow表
        int64_t recordCount = importFromArrowTable(connection, table, tableName);

        auto end = std::chrono::high_resolution_clock::now();
        m_stats.recordsImported = recordCount;
        m_stats.importTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        m_stats.recordsPerSecond = m_stats.importTimeMs > 0 ? 
            (m_stats.recordsImported * 1000.0) / m_stats.importTimeMs : 0;

        return recordCount;

    } catch (const std::exception& e) {
        setError("Exception in importMFTData: " + std::string(e.what()));
        return -1;
    }
}

std::string ArrowDuckDBIntegration::getLastError() const {
    return m_lastError;
}

ArrowDuckDBIntegration::PerformanceStats ArrowDuckDBIntegration::getPerformanceStats() const {
    return m_stats;
}

void ArrowDuckDBIntegration::resetPerformanceStats() {
    m_stats = PerformanceStats();
    m_lastError.clear();
}

void ArrowDuckDBIntegration::setError(const std::string& error) {
    m_lastError = error;
    qDebug() << "ArrowDuckDBIntegration Error:" << QString::fromStdString(error);
}

bool ArrowDuckDBIntegration::executeSql(duckdb_connection connection, const std::string& sql) {
    duckdb_result result;
    duckdb_state state = duckdb_query(connection, sql.c_str(), &result);
    
    if (state != DuckDBSuccess) {
        std::string error = "SQL execution failed: " + sql;
        if (result.error_message) {
            error += " - " + std::string(result.error_message);
        }
        setError(error);
        duckdb_destroy_result(&result);
        return false;
    }
    
    duckdb_destroy_result(&result);
    return true;
}
