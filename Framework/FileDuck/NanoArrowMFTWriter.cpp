/**
 * @file NanoArrowMFTWriter.cpp
 * @brief 基于nanoarrow的轻量级MFT数据写入器实现
 */

#include "NanoArrowMFTWriter.h"
#include <QDebug>
#include <QTemporaryFile>
#include <QDir>
#include <chrono>
#include <sstream>

NanoArrowMFTWriter::NanoArrowMFTWriter()
    : m_enableCompression(true)
    , m_batchSize(50000)
{
    resetPerformanceStats();
}

NanoArrowMFTWriter::~NanoArrowMFTWriter() {
    // 自动清理
}

int64_t NanoArrowMFTWriter::writeMFTToDuckDB(duckdb_connection connection,
                                            const std::vector<FileSystemInfoDuck>& fileInfos,
                                            const std::string& tableName,
                                            int64_t startId) {
    if (!connection || fileInfos.empty()) {
        setError("Invalid connection or empty file infos");
        return -1;
    }

    auto start = std::chrono::high_resolution_clock::now();
    m_stats.method = "direct";

    try {
        // 直接使用CSV方式（最兼容的方法）
        // DuckDB的Arrow接口在不同版本中可能不可用，所以使用CSV作为高性能替代方案
        return writeMFTViaParquet(connection, fileInfos, tableName, startId);

    } catch (const std::exception& e) {
        setError("Exception in writeMFTToDuckDB: " + std::string(e.what()));
        return -1;
    }
}

int64_t NanoArrowMFTWriter::writeMFTBatched(duckdb_connection connection,
                                           const std::vector<FileSystemInfoDuck>& fileInfos,
                                           const std::string& tableName,
                                           int64_t startId,
                                           size_t batchSize) {
    if (!connection || fileInfos.empty()) {
        setError("Invalid connection or empty file infos");
        return -1;
    }

    auto start = std::chrono::high_resolution_clock::now();
    m_stats.method = "batched";
    int64_t totalWritten = 0;

    try {
        size_t totalRecords = fileInfos.size();
        size_t processed = 0;

        while (processed < totalRecords) {
            size_t currentBatchSize = std::min(batchSize, totalRecords - processed);
            
            // 创建当前批次的子向量
            std::vector<FileSystemInfoDuck> batch(
                fileInfos.begin() + processed,
                fileInfos.begin() + processed + currentBatchSize
            );

            // 写入当前批次
            int64_t batchWritten = writeMFTViaParquet(connection, batch, tableName, 
                                                     startId + processed);
            if (batchWritten < 0) {
                setError("Failed to write batch starting at " + std::to_string(processed));
                return -1;
            }

            totalWritten += batchWritten;
            processed += currentBatchSize;

            // 输出进度
            if (processed % (batchSize * 4) == 0 || processed >= totalRecords) {
                qDebug() << "NanoArrow batch progress:" << processed << "/" << totalRecords 
                        << "(" << (processed * 100 / totalRecords) << "%)";
            }
        }

        auto end = std::chrono::high_resolution_clock::now();
        m_stats.writeTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        m_stats.recordsWritten = totalWritten;
        m_stats.recordsPerSecond = m_stats.writeTimeMs > 0 ? 
            (m_stats.recordsWritten * 1000.0) / m_stats.writeTimeMs : 0;

        return totalWritten;

    } catch (const std::exception& e) {
        setError("Exception in writeMFTBatched: " + std::string(e.what()));
        return -1;
    }
}

int64_t NanoArrowMFTWriter::writeMFTViaParquet(duckdb_connection connection,
                                              const std::vector<FileSystemInfoDuck>& fileInfos,
                                              const std::string& tableName,
                                              int64_t startId) {
    if (!connection || fileInfos.empty()) {
        setError("Invalid connection or empty file infos");
        return -1;
    }

    auto start = std::chrono::high_resolution_clock::now();
    m_stats.method = "parquet";

    try {
        // 生成临时Parquet文件
        std::string tempFile = generateTempFileName();
        
        // 创建CSV格式的临时文件（简化实现）
        QTemporaryFile csvFile;
        csvFile.setAutoRemove(true);
        if (!csvFile.open()) {
            setError("Failed to create temporary CSV file");
            return -1;
        }

        // 写入CSV头
        QTextStream stream(&csvFile);
        stream << "id,file_name,file_path,file_size,mft_entry,is_directory,is_deleted,"
               << "created_time,modified_time,accessed_time,disk_identifier,scan_time\n";

        // 写入数据
        for (size_t i = 0; i < fileInfos.size(); ++i) {
            const auto& info = fileInfos[i];
            stream << (startId + static_cast<int64_t>(i)) << ","
                   << "\"" << QString::fromStdString(info.getFileName()).replace("\"", "\"\"") << "\","
                   << "\"" << QString::fromStdString(info.getFilePath()).replace("\"", "\"\"") << "\","
                   << info.getFileSize() << ","
                   << info.getMftEntry() << ","
                   << (info.getIsDirectory() ? "true" : "false") << ","
                   << (info.getIsDeleted() ? "true" : "false") << ","
                   << "\"" << QString::fromStdString(info.getCreatedTime()) << "\","
                   << "\"" << QString::fromStdString(info.getModifiedTime()) << "\","
                   << "\"" << QString::fromStdString(info.getAccessedTime()) << "\","
                   << "\"" << QString::fromStdString(info.getDiskIdentifier()) << "\","
                   << "\"" << QString::fromStdString(info.getScanTime()) << "\"\n";
        }

        csvFile.flush();
        QString csvPath = csvFile.fileName();

        // 使用DuckDB的COPY命令导入CSV
        std::stringstream sql;
        sql << "INSERT INTO " << tableName << " SELECT * FROM read_csv_auto('" 
            << csvPath.toStdString() << "', header=true)";

        if (!executeSql(connection, sql.str())) {
            setError("Failed to import CSV data to DuckDB");
            return -1;
        }

        auto end = std::chrono::high_resolution_clock::now();
        m_stats.writeTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        m_stats.recordsWritten = fileInfos.size();
        m_stats.recordsPerSecond = m_stats.writeTimeMs > 0 ? 
            (m_stats.recordsWritten * 1000.0) / m_stats.writeTimeMs : 0;

        return m_stats.recordsWritten;

    } catch (const std::exception& e) {
        setError("Exception in writeMFTViaParquet: " + std::string(e.what()));
        return -1;
    }
}

bool NanoArrowMFTWriter::convertMFTToArrowStream(const std::vector<FileSystemInfoDuck>& fileInfos,
                                                int64_t startId,
                                                duckdb_arrow_stream* stream) {
    // 注意：这个方法需要DuckDB的Arrow支持
    // 如果DuckDB版本不支持，会返回false，然后回退到其他方法
    
    // 这里是一个简化的实现，实际需要根据DuckDB的具体Arrow API来实现
    // 由于DuckDB的Arrow接口可能因版本而异，我们先返回false，使用CSV回退方案
    
    setError("Direct Arrow stream conversion not implemented, using CSV fallback");
    return false;
}

std::string NanoArrowMFTWriter::createArrowSchemaSQL(const std::string& tableName) {
    std::stringstream sql;
    sql << "CREATE TABLE IF NOT EXISTS " << tableName << " ("
        << "id BIGINT PRIMARY KEY, "
        << "file_name VARCHAR, "
        << "file_path VARCHAR, "
        << "file_size BIGINT, "
        << "mft_entry BIGINT, "
        << "is_directory BOOLEAN, "
        << "is_deleted BOOLEAN, "
        << "created_time VARCHAR, "
        << "modified_time VARCHAR, "
        << "accessed_time VARCHAR, "
        << "disk_identifier VARCHAR, "
        << "scan_time VARCHAR"
        << ")";
    return sql.str();
}

bool NanoArrowMFTWriter::executeSql(duckdb_connection connection, const std::string& sql) {
    duckdb_result result;
    duckdb_state state = duckdb_query(connection, sql.c_str(), &result);
    
    if (state == DuckDBError) {
        setError("SQL execution failed: " + std::string(duckdb_result_error(&result)));
        duckdb_destroy_result(&result);
        return false;
    }
    
    duckdb_destroy_result(&result);
    return true;
}

std::string NanoArrowMFTWriter::generateTempFileName() {
    QTemporaryFile tempFile;
    tempFile.setFileTemplate(QDir::tempPath() + "/mft_data_XXXXXX.csv");
    tempFile.open();
    QString fileName = tempFile.fileName();
    tempFile.close();
    return fileName.toStdString();
}

void NanoArrowMFTWriter::setWriteOptions(bool enableCompression, size_t batchSize) {
    m_enableCompression = enableCompression;
    m_batchSize = batchSize;
}

std::string NanoArrowMFTWriter::getLastError() const {
    return m_lastError;
}

NanoArrowMFTWriter::PerformanceStats NanoArrowMFTWriter::getPerformanceStats() const {
    return m_stats;
}

void NanoArrowMFTWriter::resetPerformanceStats() {
    m_stats = PerformanceStats();
}

void NanoArrowMFTWriter::setError(const std::string& error) {
    m_lastError = error;
    qWarning() << "NanoArrowMFTWriter Error:" << QString::fromStdString(error);
}
